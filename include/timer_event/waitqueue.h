#ifndef __WAITQUEUE_H__
#define __WAITQUEUE_H__

#include <list.h>
#include <spinlock.h>
#include <ttos.h>

#define INIT_WAITQUEUE(__wq, __p)                                              \
    do                                                                         \
    {                                                                          \
        INIT_SPIN_LOCK (&((__wq)->lock));                                      \
        (__wq)->priv       = (__p);                                            \
    } while (0);

#define __WAITQUEUE_INITIALIZER(__wq, __p)                                     \
    {                                                                          \
        .lock       = __SPINLOCK_INITIALIZER ((__wq).lock),                    \
        .task_list  = { &(__wq).task_list, &(__wq).task_list },                \
        .task_count = 0, .priv = (__p),                                        \
    }

#define DECLARE_WAITQUEUE(__n, __p)                                            \
    struct waitqueue __n = __WAITQUEUE_INITIALIZER (__n, __p)

int  waitqueue_wake (struct timer_event *event);
int  waitqueue_sleep_with_handler (u64 expiry_ns, void (*handler) (struct timer_event *));
void waitqueue_timeout (struct timer_event *event);
#endif /* __WAITQUEUE_H__ */