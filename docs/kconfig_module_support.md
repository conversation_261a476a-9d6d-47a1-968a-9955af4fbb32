# Kconfig M 选项支持说明

## 概述

本项目现已支持 Kconfig 的 M 选项（模块化编译），允许将驱动程序编译为可加载的内核模块，而不是直接编译到内核中。

## 功能特性

1. **Tristate 配置支持**：驱动配置现在支持 `y`（内建）、`n`（禁用）、`m`（模块）三种状态
2. **自动模块构建**：配置为 `m` 的驱动会自动生成独立的模块构建脚本
3. **条件编译**：驱动代码可以根据是否为模块进行条件编译
4. **模块信息支持**：模块包含完整的许可证、作者、描述等信息

## 使用方法

### 1. 配置驱动为模块

在 menuconfig 中，支持模块化的驱动会显示为 tristate 选项：

```
[*] SemiDrive eth  --->
    < >   Disable
    <*>   Built-in  
    <M>   Module
```

选择 `<M>` 将驱动编译为模块。

### 2. 编译内核和模块

```bash
# 编译内核（不包含模块化的驱动）
make

# 编译所有模块
make modules

# 或者编译特定模块
make semidrve_module
```

### 3. 加载模块

编译完成后，模块文件（.ko）位于 `build/modules/` 目录下：

```bash
# 加载模块
insmod build/modules/semidrve/build/semidrve.ko

# 卸载模块
rmmod semidrve
```

## 支持的驱动

目前已支持模块化的驱动：

- **CONFIG_SEMIDRIVE_ETH**：SemiDrive 以太网驱动

## 添加新的模块化驱动

### 1. 修改 Kconfig

将驱动的配置从 `bool` 改为 `tristate`：

```kconfig
config MY_DRIVER
    tristate "My Driver"
    default n
    help
      My driver description.
      
      If you want to compile this as a module, choose M here.
      The module will be called my_driver.ko.
```

### 2. 修改 CMakeLists.txt

使用新的模块化宏：

```cmake
# 替换原来的 ADD_SUBDIR_IF
ADD_SUBDIR_IF_ENABLED(CONFIG_MY_DRIVER my_driver)
```

### 3. 修改驱动源代码

添加模块化支持：

```c
#ifdef CONFIG_MY_DRIVER_MODULE
#include <module.h>
#endif

// 驱动初始化函数
static int my_driver_init(void)
{
    // 初始化代码
    return 0;
}

// 驱动退出函数
static void my_driver_exit(void)
{
    // 清理代码
}

#ifdef CONFIG_MY_DRIVER_MODULE
/* 模块信息 */
MODULE_LICENSE("GPL");
MODULE_AUTHOR("Your Name");
MODULE_DESCRIPTION("My Driver");
MODULE_VERSION("1.0");

/* 模块初始化和退出 */
module_init(my_driver_init);
module_exit(my_driver_exit);
#else
/* 内建时使用传统初始化 */
INIT_EXPORT_DRIVER(my_driver_init, "my driver");
#endif
```

## 技术实现

### 构建系统扩展

1. **tools.cmake**：添加了模块化支持的宏
   - `CONFIG_IS_MODULE`：检查配置是否为模块
   - `CONFIG_IS_ENABLED`：检查配置是否启用
   - `ADD_SUBDIR_IF_ENABLED`：条件添加子目录，支持模块化

2. **loadDOTCONFIG.cmake**：扩展了配置处理
   - 自动为 `m` 状态的配置生成 `_MODULE` 宏定义
   - 维护模块列表用于后续构建

3. **module.cmake**：独立的模块构建系统
   - 设置正确的编译器和标志
   - 提供 `add_kernel_module` 函数

### 模块构建流程

1. 配置阶段：识别 `m` 状态的配置，记录到模块列表
2. 内核构建：跳过模块化的驱动，只编译内建部分
3. 模块构建：为每个模块生成独立的构建脚本并编译

## 注意事项

1. **依赖关系**：确保模块的依赖项在运行时可用
2. **符号导出**：模块使用的内核符号需要正确导出
3. **初始化顺序**：模块加载顺序可能影响系统功能
4. **内存管理**：模块卸载时需要正确清理资源

## 故障排除

### 编译错误

1. **符号未定义**：检查是否包含了正确的头文件
2. **链接错误**：确保模块链接脚本存在且正确

### 运行时错误

1. **模块加载失败**：检查模块格式和依赖关系
2. **符号解析失败**：确保内核导出了必要的符号

## 示例

参考 `test_module_config.txt` 文件查看完整的配置示例。
