/* 简单的测试模块，避免复杂的头文件包含 */

#ifdef CONFIG_TEST_MODULE_MODULE
/* 模块化编译时的简单定义 */
#define MODULE_LICENSE(x)
#define MODULE_AUTHOR(x)
#define MODULE_DESCRIPTION(x)
#define MODULE_VERSION(x)

/* 简单的模块初始化和退出宏 */
#define module_init(fn) \
    int init_module(void) { return fn(); }

#define module_exit(fn) \
    void cleanup_module(void) { fn(); }

/* 简单的打印函数声明 */
extern int printk(const char *fmt, ...);

#else
/* 内建时使用传统初始化 */
extern int printk(const char *fmt, ...);
#define INIT_EXPORT_DRIVER(fn, desc) \
    static int __init_##fn(void) { return fn(); } \
    static void *__init_ptr_##fn __attribute__((section(".init_array"))) = __init_##fn;
#endif

static int test_module_init(void)
{
    printk("Test module loaded successfully!\n");
    return 0;
}

static void test_module_exit(void)
{
    printk("Test module unloaded\n");
}

#ifdef CONFIG_TEST_MODULE_MODULE
/* 模块信息 */
MODULE_LICENSE("GPL");
MODULE_AUTHOR("TTOS Team");
MODULE_DESCRIPTION("Test Module for Kconfig M Option");
MODULE_VERSION("1.0");

/* 模块初始化和退出 */
module_init(test_module_init);
module_exit(test_module_exit);
#else
/* 内建时使用传统初始化 */
INIT_EXPORT_DRIVER(test_module_init, "test module");
#endif
