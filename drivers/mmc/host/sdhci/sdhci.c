#include <driver/devbus.h>
#include <driver/device.h>
#include <driver/driver.h>
#include <driver/of.h>
#include <ttos_pic.h>
#include <ttos_init.h>
#include <completion.h>
#include <ttosMM.h>
#include <cache.h>
#include <io.h>
#include "sdhci.h"

#include <page.h>
#include <errno.h>
#include <kmalloc.h>

#define KLOG_LEVEL  KLOG_DEBUG
#define KLOG_TAG    "SDHCI"
#include <klog.h>
#include <fs/fs.h>

#define SDHCI_CMD_TYPE_NORMAL         (0 << 6)
#define SDHCI_CMD_TYPE_SUSPEND        (1 << 6)
#define SDHCI_CMD_TYPE_RESUME         (2 << 6)
#define SDHCI_CMD_TYPE_ABORT          (3 << 6)

#define CMD_RES_NULL        (0x00)
#define CMD_RES_R2          (SDHCI_CMD_CRC | SDHCI_CMD_RESP_LONG)
#define CMD_RES_R3_R4       (SDHCI_CMD_RESP_SHORT)
#define CMD_RES_R1_R567     (SDHCI_CMD_CRC | SDHCI_CMD_INDEX | SDHCI_CMD_RESP_SHORT)
#define CMD_RES_R1b_R5b     (SDHCI_CMD_CRC | SDHCI_CMD_INDEX | SDHCI_CMD_RESP_SHORT_BUSY)

#define SDHCI_CMD(index, type, data, response)  (((index) << 8) | (type) | (data) | (response))

typedef struct sdhci_dev {
    struct devaddr_region       base;
    uint32_t                    irq;
    uint8_t                     *ext_csd;
    MUTEX_ID                    mutex;
    T_TTOS_CompletionControl    transfer_commletion;
    bool                        is_version_v4;
} *sdhci_dev_t;

static inline void sdhci_address_set(sdhci_dev_t host, phys_addr_t addr)
{
    if (host->is_version_v4)
        writeq(addr & (-1ULL), host->base.vaddr + SDHCI_DMA_ADDRESS_V4);
    else
        writel(addr & (-1UL), host->base.vaddr + SDHCI_DMA_ADDRESS_V3);
}

static inline phys_addr_t sdhci_address_get(sdhci_dev_t host)
{
    if (host->is_version_v4)
        return readq(host->base.vaddr + SDHCI_DMA_ADDRESS_V4);
    else
        return readl(host->base.vaddr + SDHCI_DMA_ADDRESS_V4);
}

static inline void sdhci_blknum_set(sdhci_dev_t host, uint32_t blk_num)
{
    if (host->is_version_v4)
        writel(blk_num, host->base.vaddr + SDHCI_BLOCK_COUNT_V4);
    else
        writew(blk_num, host->base.vaddr + SDHCI_BLOCK_COUNT_V3);
}

static int sdhci_cmd_send(sdhci_dev_t host, uint32_t cmd, uint32_t arg,
                            void *data, uint32_t blk_num, uint32_t is_read)
{
    uint32_t mode = SDHCI_TRNS_DMA | SDHCI_TRNS_BLK_CNT_EN;
    phys_addr_t dma_paddr = mm_kernel_v2p((long) data);

    if (data)
    {
        writel(arg, host->base.vaddr + SDHCI_ARGUMENT);
		sdhci_address_set(host, dma_paddr);//writel(dma_paddr & 0xFFFFFFFFU, host->base.vaddr + SDHCI_DMA_ADDRESS);
        mode |= is_read;
        if (blk_num > 1)
        {
            mode |= SDHCI_TRNS_MULTI | SDHCI_TRNS_AUTO_CMD12;
        }
    }

    writew(mode, host->base.vaddr + SDHCI_TRANSFER_MODE);
	sdhci_blknum_set(host, blk_num);//writew(blk_num, host->base.vaddr + SDHCI_BLOCK_COUNT);
    writew(cmd, host->base.vaddr + SDHCI_COMMAND);

    return 0;
}

static ssize_t sdhci_read_block(sdhci_dev_t host, uint8_t *buffer, blkcnt_t offset, uint32_t num)
{
    uint32_t cmd = SDHCI_CMD((num == 1) ? 17 : 18, SDHCI_CMD_TYPE_NORMAL,
                    SDHCI_CMD_DATA, CMD_RES_R1_R567);

    size_t size = PAGE_ALIGN(num * 512);

    phys_addr_t dma_paddr = pages_alloc(page_bits(size), ZONE_DMA32);

    if (dma_paddr == 0)
    {
        printk("no enough memory");
        return -ENOMEM;
    }

    virt_addr_t dma_vaddr = page_address(dma_paddr);

    if (dma_vaddr == 0)
    {
        printk("no enough memory");
        return -ENOMEM;
    }
    /* 此处是为了避免cache换出 导致cache中的数据写入内存 所以提前无效 */
    cache_dcache_invalidate((size_t) dma_vaddr, size);

    sdhci_cmd_send(host, cmd, offset, (void *)dma_vaddr, num, SDHCI_TRNS_READ);
    TTOS_WaitCompletionUninterruptible(&host->transfer_commletion, TTOS_WAIT_FOREVER);
    cache_dcache_invalidate((size_t) dma_vaddr, size);

    memcpy(buffer, (void *)dma_vaddr, num * 512);

    pages_free(dma_paddr, page_bits(size));

    return num;
}

static ssize_t sdhci_write_block(sdhci_dev_t host, uint8_t *buffer, blkcnt_t offset, uint32_t num)
{
    uint32_t cmd = SDHCI_CMD((num == 1) ? 24 : 25, SDHCI_CMD_TYPE_NORMAL,
                    SDHCI_CMD_DATA, CMD_RES_R1_R567);
    size_t size = PAGE_ALIGN(num * 512);

    phys_addr_t dma_paddr = pages_alloc(page_bits(size), ZONE_DMA32);

    if (dma_paddr == 0)
    {
        printk("no enough memory");
        return -ENOMEM;
    }

    virt_addr_t dma_vaddr = page_address(dma_paddr);

    if (dma_vaddr == 0)
    {
        printk("no enough memory");
        return -ENOMEM;
    }

    memcpy((void *)dma_vaddr, buffer, num * 512);

    cache_dcache_clean((size_t) dma_vaddr, size);

    sdhci_cmd_send(host, cmd, offset, (void *)dma_vaddr, num, 0);
    TTOS_WaitCompletionUninterruptible(&host->transfer_commletion, TTOS_WAIT_FOREVER);

    pages_free(dma_paddr, page_bits(size));
    return num;
}

static void sdhci_handler(uint32_t irq, void *param)
{
    sdhci_dev_t host = param;
    uint32_t state;

    state = readl(host->base.vaddr + SDHCI_INT_STATUS);

    if (state & SDHCI_INT_DMA_END)
    {
		sdhci_address_set(host, sdhci_address_get(host));//writel(readl(host->base.vaddr + SDHCI_DMA_ADDRESS), host->base.vaddr);
    }

    if (state & SDHCI_INT_DATA_END)
    {
        TTOS_ReleaseCompletion(&host->transfer_commletion);
    }

    writel(state, host->base.vaddr + SDHCI_INT_STATUS);

    // KLOG_W("sdhci_handler state: 0x%X  CMD err:0x%X", state,
    //         readw(host->base.vaddr + SDHCI_AUTO_CMD_STATUS));
}

static int sdhci_blk_open (struct inode *inode)
{
    return 0;
}

static int sdhci_blk_close (struct inode *inode)
{
    return 0;
}

static ssize_t sdhci_blk_read (struct inode *inode, unsigned char *buffer,
                                blkcnt_t startsector, unsigned int nsectors)
{
    sdhci_dev_t host = inode->i_private;
    ssize_t num;

    // KLOG_I("sdhci read  start:%d  num: %d", startsector, nsectors);
    TTOS_ObtainMutex(host->mutex, TTOS_WAIT_FOREVER);
    num = sdhci_read_block(host, buffer, startsector, nsectors);
    TTOS_ReleaseMutex(host->mutex);

    return num;
}

static ssize_t sdhci_blk_write (struct inode *inode, const unsigned char *buffer,
                      blkcnt_t start_sector, unsigned int nsectors)
{
    sdhci_dev_t host = inode->i_private;
    ssize_t num;

    // KLOG_I("sdhci write  start:%d  num: %d", start_sector, nsectors);
    TTOS_ObtainMutex(host->mutex, TTOS_WAIT_FOREVER);
    num = sdhci_write_block(host, (uint8_t *) buffer, start_sector, nsectors);
    TTOS_ReleaseMutex(host->mutex);

    return num;
}

static int sdhci_blk_geometry(struct inode *inode, struct geometry *geometry)
{
    sdhci_dev_t host;
    int ret = -EINVAL;

    host = inode->i_private;

    if (geometry)
    {
        geometry->geo_available    = true;
        geometry->geo_mediachanged = false;
        geometry->geo_writeenabled = true;
        geometry->geo_nsectors     = readl(host->ext_csd + 212);
        geometry->geo_sectorsize   = 512;
        ret                        = 0;
    }

    return ret;
}


static const struct block_operations sdhci_blk_bops = {
    sdhci_blk_open,     /* open     */
    sdhci_blk_close,    /* close    */
    sdhci_blk_read,     /* read     */
    sdhci_blk_write,    /* write    */
    sdhci_blk_geometry, /* geometry */
    // sdhci_blk_ioctl     /* ioctl    */
};

struct sdhci_dev *sdhci_dbg;
static int shdci_probe(struct device *dev)
{
    int ret;
    uint32_t reg;
    struct sdhci_dev *sdhci;

    sdhci = malloc(sizeof(struct sdhci_dev));
    dev_priv_set(dev, sdhci);
    sdhci->ext_csd = memalign(0x1000, 512);

    platform_get_resource_regs(dev, &sdhci->base, 1);
    KLOG_I("SDHCI version:       0x%X", readw(sdhci->base.vaddr + SDHCI_HOST_VERSION));
    KLOG_I("SDHCI blk size:      0x%X", readw(sdhci->base.vaddr + SDHCI_BLOCK_SIZE));
    KLOG_I("SDHCI host ctr:      0x%X", readb(sdhci->base.vaddr + SDHCI_HOST_CONTROL));
    KLOG_I("SDHCI host ctr2:     0x%X", readw(sdhci->base.vaddr + SDHCI_HOST_CONTROL2));
    KLOG_I("SDHCI TRANSFER MODE: 0x%X", readw(sdhci->base.vaddr + SDHCI_TRANSFER_MODE));

    sdhci->is_version_v4 = readw(sdhci->base.vaddr + SDHCI_HOST_CONTROL2) & SDHCI_CTRL_V4_MODE;

    writeb(SDHCI_RESET_DATA, sdhci->base.vaddr + SDHCI_SOFTWARE_RESET);

    writel(SDHCI_INT_ALL_MASK, sdhci->base.vaddr + SDHCI_INT_STATUS);
    if(sdhci->is_version_v4)
    {
        reg = readb(sdhci->base.vaddr + SDHCI_HOST_CONTROL) & ~SDHCI_CTRL_DMA_MASK;
        writeb(reg | SDHCI_CTRL_SDMA, sdhci->base.vaddr + SDHCI_HOST_CONTROL);
	}
    writew(SDHCI_TRNS_READ | SDHCI_TRNS_MULTI | SDHCI_TRNS_DMA |
            SDHCI_TRNS_BLK_CNT_EN | SDHCI_TRNS_AUTO_CMD12,
            sdhci->base.vaddr + SDHCI_TRANSFER_MODE);

    sdhci->irq = ttos_pic_irq_alloc(dev, 0);
    ttos_pic_irq_install(sdhci->irq, sdhci_handler, sdhci, 0, "sdhci");
    ttos_pic_irq_unmask(sdhci->irq);
    if(sdhci->is_version_v4)
    {
    	writew(SDHCI_MAKE_BLKSZ(1, 512), sdhci->base.vaddr + SDHCI_BLOCK_SIZE);
    }
    writel(0xFFFFFFFF, sdhci->base.vaddr + SDHCI_SIGNAL_ENABLE);
    writel(0xFFFFFFFF & ~SDHCI_INT_CARD_INT, sdhci->base.vaddr + SDHCI_INT_ENABLE);
    writew(0xFFFF, sdhci->base.vaddr + SDHCI_AUTO_CMD_STATUS);

    TTOS_CreateMutex(1, 0, &sdhci->mutex);
    TTOS_InitCompletion(&sdhci->transfer_commletion);

    sdhci_cmd_send(sdhci,
        SDHCI_CMD(8, SDHCI_CMD_TYPE_NORMAL, SDHCI_CMD_DATA, CMD_RES_R1_R567),
        0, sdhci->ext_csd, 1, SDHCI_TRNS_READ);
    TTOS_WaitCompletion(&sdhci->transfer_commletion, TTOS_WAIT_FOREVER);

    KLOG_I("SDHCI sectors num: %d", readl(sdhci->ext_csd + 212));

    ret = register_blockdriver("/dev/mmcblk", &sdhci_blk_bops, 0660, sdhci);
    if (ret < 0)
    {
        KLOG_E ("Register block driver failed, ret=%d", ret);
    }

    sdhci_dbg = sdhci;

    return 0;
}

static struct of_device_id sdhci_table[] = {
    {.compatible = "snps,dwcmshc-sdhci",},
    {.compatible = "rockchip,dwcmshc-sdhci",},
    {.compatible = "rockchip,rk3568-dwcmshc",},
    {.compatible = "rockchip,rk3562-dwcmshc",},
    { /* end of list */ },
};

static struct driver sdhci_driver = {
    .name        = "SDHCI",
    .probe       = shdci_probe,
    .match_table = sdhci_table,
};

static int32_t sdhci_driver_init(void)
{
    return platform_add_driver(&sdhci_driver);
}
INIT_EXPORT_DRIVER(sdhci_driver_init, "SDHCI driver");
