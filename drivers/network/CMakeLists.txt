string(REGEX REPLACE "/$" "" CURRENT_FOLDER_ABSOLUTE ${CMAKE_CURRENT_SOURCE_DIR})

file(GLOB SOURCE_FILE_LIST ${CURRENT_FOLDER_ABSOLUTE}/*.c ${CURRENT_FOLDER_ABSOLUTE}/*.S ${CURRENT_FOLDER_ABSOLUTE}/*.cpp ${CURRENT_FOLDER_ABSOLUTE}/*.cxx ${CURRENT_FOLDER_ABSOLUTE}/*.c++)

foreach(file ${SOURCE_FILE_LIST})
    list(APPEND GLOB_SOURCE_LIST ${file})
endforeach()

# 包含子文件夹
SUBDIRLIST(SUBDIRS)

foreach(SUB_FOLDER ${SUBDIRS})
    ADD_SUBDIR(${SUB_FOLDER})
endforeach()

# 确保模块列表变量在父作用域中可见
set(KERNEL_MODULES_LIST ${KERNEL_MODULES_LIST} PARENT_SCOPE)
set(KERNEL_MODULES_CONFIG_LIST ${KERNEL_MODULES_CONFIG_LIST} PARENT_SCOPE)

set(GLOB_INC_PATH_LIST ${GLOB_INC_PATH_LIST} PARENT_SCOPE)
set(GLOB_SOURCE_LIST ${GLOB_SOURCE_LIST} PARENT_SCOPE)
