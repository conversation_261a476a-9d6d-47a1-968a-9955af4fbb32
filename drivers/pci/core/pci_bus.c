#include <driver/pci/pci_host.h>
#include <errno.h>
#include <fs/fs.h>
#include <stdio.h>
#include <system/macros.h>
#include <ttos_init.h>

#undef KLOG_TAG
#undef KLOG_LEVEL
#define KLOG_TAG "pci-bus"
#define KLOG_LEVEL KLOG_DEBUG
#include <klog.h>

static uint32_t busno = 0;

int pci_register_device(struct pci_dev *device)
{
    device->dev.bus = device->host->bus;

    return register_device(&device->dev);
}

int pci_register_driver(struct pci_driver *driver)
{
    struct bus_type *bus;
    struct pci_driver *reg_driver;
    int bus_no;

    for (bus_no = 0; bus_no < 256; bus_no++)
    {
        bus = devbus_get_bus(PCI_BUS, bus_no);

        if (bus)
        {
            reg_driver = malloc(sizeof(struct pci_driver));
            memcpy(reg_driver, driver, sizeof(struct pci_driver));
            INIT_LIST_HEAD(&reg_driver->driver.head);
            devbus_add_driver(bus, &reg_driver->driver);
        }
    }

    return 0;
}

static inline int pci_ids_valid(const struct pci_device_id *ids)
{
    return (ids->device | ids->vendor | ids->subdevice | ids->subvendor | ids->class) ? 1 : 0;
}

static int pci_bus_match(struct device *dev, struct driver *drv)
{
    struct pci_dev *pci_device;
    struct pci_driver *pci_driver;
    const struct pci_device_id *ids;
    int match;

    if (!dev || !drv)
    {
        return 0;
    }

    pci_device = container_of(dev, struct pci_dev, dev);
    pci_driver = container_of(drv, struct pci_driver, driver);
    ids = pci_driver->id_table;
    match = 0;

    for (; pci_ids_valid(ids); ids++)
    {
        if (ids->class && ((pci_device->class & ids->class_mask) == ids->class))
        {
            match = 1;
            break;
        }
        else if ((ids->device != PCI_ANY_ID) && (ids->vendor != PCI_ANY_ID) &&
                 (ids->device == pci_device->device) && (ids->vendor == pci_device->vendor))
        {
            if ((ids->subdevice != PCI_ANY_ID) && (ids->subvendor != PCI_ANY_ID) &&
                (ids->subdevice != pci_device->subsystem_device) &&
                (ids->subvendor != pci_device->subsystem_vendor))
            {
                break;
            }

            match = 1;
            break;
        }
    }

    if (match)
    {
        pci_device->priv = (void *)ids;
        pci_device->driver = pci_driver;
    }

    return match;
}

static int pci_bus_probe(struct device *dev)
{
    struct pci_dev *pci_device;
    struct pci_driver *pci_driver;
    int rc = -1;

    pci_device = container_of(dev, struct pci_dev, dev);
    pci_driver = pci_device->driver;

    if (pci_driver->probe)
    {
        rc = pci_driver->probe(pci_device, (const struct pci_device_id *)pci_device->priv);
    }

    return rc;
}

static int pci_bus_remove(struct device *dev)
{
    return 0;
}

static struct bus_type pci_bus_type = {
    .type_index = PCI_BUS,
    .match = pci_bus_match,
    .probe = pci_bus_probe,
    .remove = pci_bus_remove,
};

int pci_bus_register(struct pci_host *root_device)
{
    int ret;
    struct bus_type *bus_type;

    if (!root_device)
        return -1;

    bus_type = calloc(1, sizeof(struct bus_type));
    if (!(bus_type))
        return -ENOMEM;

    root_device->bus = bus_type;
    dev_priv_set(root_device->parent, root_device);

    memcpy(bus_type, &pci_bus_type, sizeof(struct bus_type));

    bus_type->priv = root_device;
    bus_type->bus_index = busno;

    sprintf(bus_type->name, "pci-root%d", busno++);

    ret = devbus_register(bus_type);
    if (ret < 0)
    {
        free(bus_type);
        return -1;
    }

    return 0;
}
