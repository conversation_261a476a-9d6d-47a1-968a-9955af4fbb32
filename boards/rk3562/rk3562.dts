/dts-v1/;

/ {
	compatible = "rockchip,rk3562-evb2-ddr4-v10\0rockchip,rk3562";
	interrupt-parent = <0x01>;
	#address-cells = <0x02>;
	#size-cells = <0x02>;
	model = "TOPEET RK3562 EVB2 DDR4 Board";

	aliases {
		csi2dphy0 = "/csi2-dphy0";
		csi2dphy1 = "/csi2-dphy1";
		csi2dphy2 = "/csi2-dphy2";
		csi2dphy3 = "/csi2-dphy3";
		csi2dphy4 = "/csi2-dphy4";
		csi2dphy5 = "/csi2-dphy5";
		ethernet0 = "/ethernet@ffa80000";
		ethernet1 = "/ethernet@ffb30000";
		gpio0 = "/pinctrl/gpio@ff260000";
		gpio1 = "/pinctrl/gpio@ff620000";
		gpio2 = "/pinctrl/gpio@ff630000";
		gpio3 = "/pinctrl/gpio@ffac0000";
		gpio4 = "/pinctrl/gpio@ffad0000";
		i2c0 = "/i2c@ff200000";
		i2c1 = "/i2c@ffa00000";
		i2c2 = "/i2c@ffa10000";
		i2c3 = "/i2c@ffa20000";
		i2c4 = "/i2c@ffa30000";
		i2c5 = "/i2c@ffa40000";
		rkcif_mipi_lvds0 = "/rkcif-mipi-lvds";
		rkcif_mipi_lvds1 = "/rkcif-mipi-lvds1";
		rkcif_mipi_lvds2 = "/rkcif-mipi-lvds2";
		rkcif_mipi_lvds3 = "/rkcif-mipi-lvds3";
                mmc0 = "/sdhci@ff870000";
		serial0 = "/serial@ff210000";
		serial1 = "/serial@ff670000";
		serial2 = "/serial@ff680000";
		serial3 = "/serial@ff690000";
		serial4 = "/serial@ff6a0000";
		serial5 = "/serial@ff6b0000";
		serial6 = "/serial@ff6c0000";
		serial7 = "/serial@ff6d0000";
		serial8 = "/serial@ff6e0000";
		serial9 = "/serial@ff6f0000";
		spi0 = "/spi@ff220000";
		spi1 = "/spi@ff640000";
		spi2 = "/spi@ff650000";
		spi3 = "/spi@ff860000";
	};

	clocks {
		compatible = "simple-bus";
		#address-cells = <0x02>;
		#size-cells = <0x02>;
		ranges;

		xin32k {
			compatible = "fixed-clock";
			#clock-cells = <0x00>;
			clock-frequency = <0x8000>;
			clock-output-names = "xin32k";
		};

		xin24m {
			compatible = "fixed-clock";
			#clock-cells = <0x00>;
			clock-frequency = <0x16e3600>;
			clock-output-names = "xin24m";
		};

		hclk_vepu@ff100324 {
			compatible = "rockchip,rk3562-clock-gate-link";
			reg = <0x00 0xff100324 0x00 0x10>;
			clock-names = "link";
			clocks = <0x02 0x152>;
			#power-domain-cells = <0x01>;
			#clock-cells = <0x00>;
		};

		aclk_vdpu@ff100328 {
			compatible = "rockchip,rk3562-clock-gate-link";
			reg = <0x00 0xff100328 0x00 0x10>;
			clock-names = "link";
			clocks = <0x02 0x12>;
			#power-domain-cells = <0x01>;
			#clock-cells = <0x00>;
		};

		aclk_vi_isp@ff10032c {
			compatible = "rockchip,rk3562-clock-gate-link";
			reg = <0x00 0xff10032c 0x00 0x10>;
			clock-names = "link";
			clocks = <0x02 0x12>;
			#power-domain-cells = <0x01>;
			#clock-cells = <0x00>;
			phandle = <0x03>;
		};

		aclk_vo@ff100334 {
			compatible = "rockchip,rk3562-clock-gate-link";
			reg = <0x00 0xff100334 0x00 0x10>;
			clock-names = "link";
			clocks = <0x02 0x12>;
			#power-domain-cells = <0x01>;
			#clock-cells = <0x00>;
			phandle = <0x04>;
		};

		aclk_vepu@ff100324 {
			compatible = "rockchip,rk3562-clock-gate-link";
			reg = <0x00 0xff100324 0x00 0x10>;
			clock-names = "link";
			clocks = <0x03>;
			#power-domain-cells = <0x01>;
			#clock-cells = <0x00>;
		};

		aclk_rga_jdec@ff100338 {
			compatible = "rockchip,rk3562-clock-gate-link";
			reg = <0x00 0xff100338 0x00 0x10>;
			clock-names = "link";
			clocks = <0x04>;
			#power-domain-cells = <0x01>;
			#clock-cells = <0x00>;
		};

		mclkin-sai0 {
			compatible = "fixed-clock";
			#clock-cells = <0x00>;
			clock-frequency = <0x00>;
			clock-output-names = "mclk_sai0_from_io";
		};

		mclkin-sai1 {
			compatible = "fixed-clock";
			#clock-cells = <0x00>;
			clock-frequency = <0x00>;
			clock-output-names = "mclk_sai1_from_io";
		};

		mclkin-sai2 {
			compatible = "fixed-clock";
			#clock-cells = <0x00>;
			clock-frequency = <0x00>;
			clock-output-names = "mclk_sai2_from_io";
		};

		mclkout-sai0@ff040070 {
			compatible = "rockchip,clk-out";
			reg = <0x00 0xff040070 0x00 0x04>;
			clocks = <0x02 0x79>;
			#clock-cells = <0x00>;
			clock-output-names = "mclk_sai0_to_io";
			rockchip,bit-shift = <0x04>;
			phandle = <0x45>;
		};

		mclkout-sai1@ff040070 {
			compatible = "rockchip,clk-out";
			reg = <0x00 0xff040070 0x00 0x04>;
			clocks = <0x02 0x7f>;
			#clock-cells = <0x00>;
			clock-output-names = "mclk_sai1_to_io";
			rockchip,bit-shift = <0x09>;
		};

		mclkout-sai2@ff040070 {
			compatible = "rockchip,clk-out";
			reg = <0x00 0xff040070 0x00 0x04>;
			clocks = <0x02 0x85>;
			#clock-cells = <0x00>;
			clock-output-names = "mclk_sai2_to_io";
			rockchip,bit-shift = <0x0b>;
		};
	};

	cpus {
		#address-cells = <0x02>;
		#size-cells = <0x00>;

		cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a53";
			reg = <0x00 0x00>;
			enable-method = "psci";
			clocks = <0x05 0x08>;
			cpu-idle-states = <0x06>;
			operating-points-v2 = <0x07>;
			#cooling-cells = <0x02>;
			dynamic-power-coefficient = <0x8a>;
			cpu-supply = <0x08>;
			phandle = <0x0e>;
		};

		cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a53";
			reg = <0x00 0x01>;
			enable-method = "psci";
			clocks = <0x05 0x08>;
			cpu-idle-states = <0x06>;
			operating-points-v2 = <0x07>;
			#cooling-cells = <0x02>;
			dynamic-power-coefficient = <0x8a>;
			phandle = <0x0f>;
		};

		cpu@2 {
			device_type = "cpu";
			compatible = "arm,cortex-a53";
			reg = <0x00 0x02>;
			enable-method = "psci";
			clocks = <0x05 0x08>;
			cpu-idle-states = <0x06>;
			operating-points-v2 = <0x07>;
			#cooling-cells = <0x02>;
			dynamic-power-coefficient = <0x8a>;
			phandle = <0x10>;
		};

		cpu@3 {
			device_type = "cpu";
			compatible = "arm,cortex-a53";
			reg = <0x00 0x03>;
			enable-method = "psci";
			clocks = <0x05 0x08>;
			cpu-idle-states = <0x06>;
			operating-points-v2 = <0x07>;
			#cooling-cells = <0x02>;
			dynamic-power-coefficient = <0x8a>;
			phandle = <0x11>;
		};

		idle-states {
			entry-method = "psci";

			cpu-sleep {
				compatible = "arm,idle-state";
				local-timer-stop;
				arm,psci-suspend-param = <0x10000>;
				entry-latency-us = <0x78>;
				exit-latency-us = <0xfa>;
				min-residency-us = <0x384>;
				phandle = <0x06>;
			};
		};
	};

	cpu0-opp-table {
		compatible = "operating-points-v2";
		opp-shared;
		mbist-vmin = <0xc96a8 0xdbba0 0xee098>;
		nvmem-cells = <0x09 0x0a 0x0b 0x0c>;
		nvmem-cell-names = "leakage\0opp-info\0mbist-vmin\0pvtm";
		rockchip,pvtm-voltage-sel = <0x00 0x500 0x00 0x501 0x546 0x01 0x547 0x58c 0x02 0x58d 0x5d2 0x03 0x5d3 0x270f 0x04>;
		rockchip,pvtm-pvtpll;
		rockchip,pvtm-offset = <0x634>;
		rockchip,pvtm-sample-time = <0x44c>;
		rockchip,pvtm-freq = <0x188940>;
		rockchip,pvtm-volt = <0xdbba0>;
		rockchip,pvtm-ref-temp = <0x28>;
		rockchip,pvtm-temp-prop = <0x00 0x00>;
		rockchip,pvtm-thermal-zone = "soc-thermal";
		rockchip,grf = <0x0d>;
		rockchip,temp-hysteresis = <0x1388>;
		rockchip,low-temp = <0x2710>;
		rockchip,low-temp-min-volt = <0xe1d48>;
		phandle = <0x07>;

		opp-j-od-1416000000 {
			opp-supported-hw = <0x04 0xffff>;
			opp-hz = <0x00 0x54667200>;
			opp-microvolt = <0xf4240 0xf4240 0x118c30>;
			opp-microvolt-L0 = <0xf4240 0xf4240 0x118c30>;
			opp-microvolt-L1 = <0xee098 0xee098 0x118c30>;
			opp-microvolt-L2 = <0xe7ef0 0xe7ef0 0x118c30>;
			opp-microvolt-L3 = <0xe1d48 0xe1d48 0x118c30>;
			opp-microvolt-L4 = <0xdbba0 0xdbba0 0x118c30>;
			clock-latency-ns = <0x9c40>;
			status = "disabled";
		};

		opp-j-od-1608000000 {
			opp-supported-hw = <0x04 0xffff>;
			opp-hz = <0x00 0x5fd82200>;
			opp-microvolt = <0xfd4bc 0xfd4bc 0x118c30>;
			opp-microvolt-L0 = <0xfd4bc 0xfd4bc 0x118c30>;
			opp-microvolt-L1 = <0xf7314 0xf7314 0x118c30>;
			opp-microvolt-L2 = <0xf116c 0xf116c 0x118c30>;
			opp-microvolt-L3 = <0xeafc4 0xeafc4 0x118c30>;
			opp-microvolt-L4 = <0xe4e1c 0xe4e1c 0x118c30>;
			clock-latency-ns = <0x9c40>;
			status = "disabled";
		};

		opp-j-od-1800000000 {
			opp-supported-hw = <0x04 0xffff>;
			opp-hz = <0x00 0x6b49d200>;
			opp-microvolt = <0x112a88 0x112a88 0x118c30>;
			opp-microvolt-L0 = <0x112a88 0x112a88 0x118c30>;
			opp-microvolt-L1 = <0x10c8e0 0x10c8e0 0x118c30>;
			opp-microvolt-L2 = <0x106738 0x106738 0x118c30>;
			opp-microvolt-L3 = <0x100590 0x100590 0x118c30>;
			opp-microvolt-L4 = <0xfa3e8 0xfa3e8 0x118c30>;
			clock-latency-ns = <0x9c40>;
			status = "disabled";
		};

		opp-2016000000 {
			opp-hz = <0x00 0x7829b800>;
			opp-microvolt = <0x118c30 0x118c30 0x118c30>;
			opp-microvolt-L0 = <0x118c30 0x118c30 0x118c30>;
			opp-microvolt-L1 = <0x118c30 0x118c30 0x118c30>;
			opp-microvolt-L2 = <0x112a88 0x112a88 0x118c30>;
			opp-microvolt-L3 = <0x10c8e0 0x10c8e0 0x118c30>;
			opp-microvolt-L4 = <0x106738 0x106738 0x118c30>;
			clock-latency-ns = <0x9c40>;
		};
	};

	arm-pmu {
		compatible = "arm,cortex-a53-pmu";
		interrupts = <0x00 0xe4 0x04 0x00 0xe5 0x04 0x00 0xe6 0x04 0x00 0xe7 0x04>;
		interrupt-affinity = <0x0e 0x0f 0x10 0x11>;

		opp-j-700000000 {
			opp-supported-hw = <0x04 0xffff>;
			opp-hz = <0x00 0x29b92700>;
			opp-microvolt = <0xdbba0 0xdbba0 0xf4240>;
			opp-microvolt-L0 = <0xe1d48 0xe1d48 0xf4240>;
		};

		opp-j-od-800000000 {
			opp-supported-hw = <0x04 0xffff>;
			opp-hz = <0x00 0x2faf0800>;
			opp-microvolt = <0xee098 0xee098 0xf4240>;
			opp-microvolt-L0 = <0xee098 0xee098 0xf4240>;
			opp-microvolt-L1 = <0xe7ef0 0xe7ef0 0xf4240>;
			opp-microvolt-L2 = <0xe1d48 0xe1d48 0xf4240>;
			opp-microvolt-L3 = <0xdbba0 0xdbba0 0xf4240>;
			opp-microvolt-L4 = <0xdbba0 0xdbba0 0xf4240>;
			status = "disabled";
		};

		opp-j-od-900000000 {
			opp-supported-hw = <0x04 0xffff>;
			opp-hz = <0x00 0x35a4e900>;
			opp-microvolt = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L0 = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L1 = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L2 = <0xee098 0xee098 0xf4240>;
			opp-microvolt-L3 = <0xe7ef0 0xe7ef0 0xf4240>;
			opp-microvolt-L4 = <0xe1d48 0xe1d48 0xf4240>;
			status = "disabled";
		};

		opp-j-od-1000000000 {
			opp-supported-hw = <0x04 0xffff>;
			opp-hz = <0x00 0x3b9aca00>;
			opp-microvolt = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L0 = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L1 = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L2 = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L3 = <0xee098 0xee098 0xf4240>;
			opp-microvolt-L4 = <0xe7ef0 0xe7ef0 0xf4240>;
			status = "disabled";
		};
	};

	bus-soc {
		compatible = "rockchip,rk3562-bus";
		rockchip,busfreq-policy = "smc";
		rockchip,soc-bus-table = <0x00 0xa000a8 0x7001 0x01 0xa000a8 0x7c39 0x02 0xa000a8 0x7c39 0x03 0xa000a8 0x7c39 0x04 0xa000a5 0xb007 0x05 0xa000a8 0x7034 0x06 0xa000a8 0x7034 0x07 0xa000a8 0x7034 0x08 0xa000a8 0x7001>;
	};

	cpuinfo {
		compatible = "rockchip,cpuinfo";
		nvmem-cells = <0x12 0x13 0x14>;
		nvmem-cell-names = "id\0cpu-version\0cpu-code";
	};

	csi2-dphy0 {
		compatible = "rockchip,rk3562-csi2-dphy";
		rockchip,hw = <0x15>;
		status = "okay";

		ports {
			#address-cells = <0x01>;
			#size-cells = <0x00>;

			port@0 {
				reg = <0x00>;
				#address-cells = <0x01>;
				#size-cells = <0x00>;

				endpoint@1 {
					reg = <0x01>;
					remote-endpoint = <0x16>;
					data-lanes = <0x01 0x02>;
					phandle = <0xb5>;
				};

				endpoint@2 {
					reg = <0x02>;
					remote-endpoint = <0x17>;
					data-lanes = <0x01 0x02 0x03 0x04>;
					phandle = <0xb6>;
				};
			};

			port@1 {
				reg = <0x01>;
				#address-cells = <0x01>;
				#size-cells = <0x00>;

				endpoint@0 {
					reg = <0x00>;
					remote-endpoint = <0x18>;
					phandle = <0x69>;
				};
			};
		};
	};

	csi2-dphy1 {
		compatible = "rockchip,rk3562-csi2-dphy";
		rockchip,hw = <0x15>;
		status = "disabled";
	};

	csi2-dphy2 {
		compatible = "rockchip,rk3562-csi2-dphy";
		rockchip,hw = <0x15>;
		status = "disabled";
	};

	csi2-dphy3 {
		compatible = "rockchip,rk3562-csi2-dphy";
		rockchip,hw = <0x19>;
		status = "disabled";
	};

	csi2-dphy4 {
		compatible = "rockchip,rk3562-csi2-dphy";
		rockchip,hw = <0x19>;
		status = "disabled";
	};

	csi2-dphy5 {
		compatible = "rockchip,rk3562-csi2-dphy";
		rockchip,hw = <0x19>;
		status = "disabled";
	};

	display-subsystem {
		compatible = "rockchip,display-subsystem";
		ports = <0x1a>;
		status = "okay";
		memory-region = <0x1b 0x1c>;
		memory-region-names = "drm-logo\0drm-cubic-lut";

		route {

			route-dsi {
				status = "disabled";
				logo,uboot = "logo.bmp";
				logo,kernel = "logo_kernel.bmp";
				logo,mode = "center";
				charge_logo,mode = "center";
				connect = <0x1d>;
			};

			route-lvds {
				status = "okay";
				logo,uboot = "logo.bmp";
				logo,kernel = "logo_kernel.bmp";
				logo,mode = "center";
				charge_logo,mode = "center";
				connect = <0x1e>;
			};

			route-rgb {
				status = "disabled";
				logo,uboot = "logo.bmp";
				logo,kernel = "logo_kernel.bmp";
				logo,mode = "center";
				charge_logo,mode = "center";
				connect = <0x1f>;
			};
		};
	};

	dmc {
		compatible = "rockchip,rk3562-dmc";
		interrupts = <0x00 0x76 0x04>;
		interrupt-names = "complete";
		devfreq-events = <0x20>;
		clocks = <0x05 0x0b>;
		clock-names = "dmc_clk";
		operating-points-v2 = <0x21>;
		upthreshold = <0x28>;
		downdifferential = <0x14>;
		system-status-level = <0x01 0x04 0x08 0x08 0x02 0x01 0x10 0x04 0x10000 0x04 0x1000 0x08 0x4000 0x08 0x2000 0x08 0xc00 0x08>;
		auto-min-freq = <0x4f1a0>;
		auto-freq-en = <0x01>;
		#cooling-cells = <0x02>;
		status = "disabled";
		center-supply = <0x22>;
	};

	dmc-opp-table {
		compatible = "operating-points-v2";
		mbist-vmin = <0xcf850 0xdbba0 0xe1d48>;
		nvmem-cells = <0x23 0x24 0x25>;
		nvmem-cell-names = "leakage\0opp-info\0mbist-vmin";
		rockchip,temp-hysteresis = <0x1388>;
		rockchip,low-temp = <0x2710>;
		rockchip,low-temp-min-volt = <0xdbba0>;
		rockchip,leakage-voltage-sel = <0x01 0x0f 0x00 0x10 0x14 0x01 0x15 0xfe 0x02>;
		phandle = <0x21>;

		opp-1560000000 {
			opp-hz = <0x00 0x5cfbb600>;
			opp-microvolt = <0xdbba0 0xdbba0 0xe7ef0>;
			opp-microvolt-L0 = <0xdbba0 0xdbba0 0xe7ef0>;
			opp-microvolt-L1 = <0xd59f8 0xd59f8 0xe7ef0>;
			opp-microvolt-L2 = <0xcf850 0xcf850 0xe7ef0>;
		};
	};

	firmware {

		scmi {
			compatible = "arm,scmi-smc";
			shmem = <0x26>;
			arm,smc-id = <0x82000010>;
			#address-cells = <0x01>;
			#size-cells = <0x00>;

			protocol@14 {
				reg = <0x14>;
				#clock-cells = <0x01>;
				phandle = <0x05>;
			};
		};
	};

	mpp-srv {
		compatible = "rockchip,mpp-service";
		rockchip,taskqueue-count = <0x03>;
		rockchip,resetgroup-count = <0x03>;
		status = "okay";
		phandle = <0x67>;
	};

	psci {
		compatible = "arm,psci-1.0";
		method = "smc";
	};

	reserved-memory {
		#address-cells = <0x02>;
		#size-cells = <0x02>;
		ranges;

		drm-logo@00000000 {
			compatible = "rockchip,drm-logo";
			reg = <0x00 0x00 0x00 0x00>;
			phandle = <0x1b>;
		};

		vendor-storage-rm@00000000 {
			compatible = "rockchip,vendor-storage-rm";
			reg = <0x00 0x00 0x00 0x00>;
			phandle = <0x34>;
		};

		drm-cubic-lut@00000000 {
			compatible = "rockchip,drm-cubic-lut";
			reg = <0x00 0x00 0x00 0x00>;
			phandle = <0x1c>;
		};

		ramoops@110000 {
			compatible = "ramoops";
			reg = <0x00 0x110000 0x00 0xe0000>;
			boot-log-size = <0x8000>;
			boot-log-count = <0x01>;
			console-size = <0x80000>;
			pmsg-size = <0x30000>;
			ftrace-size = <0x00>;
			record-size = <0x14000>;
		};
	};

	rkcif-mipi-lvds {
		compatible = "rockchip,rkcif-mipi-lvds";
		rockchip,hw = <0x27>;
		iommus = <0x28>;
		status = "okay";
		phandle = <0x2a>;

		port {

			endpoint {
				remote-endpoint = <0x29>;
				phandle = <0x6a>;
			};
		};
	};

	rkcif-mipi-lvds-sditf {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2a>;
		status = "okay";

		port {

			endpoint {
				remote-endpoint = <0x2b>;
				phandle = <0x30>;
			};
		};
	};

	rkcif-mipi-lvds-sditf-vir1 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2a>;
		status = "disabled";
	};

	rkcif-mipi-lvds-sditf-vir2 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2a>;
		status = "disabled";
	};

	rkcif-mipi-lvds-sditf-vir3 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2a>;
		status = "disabled";
	};

	rkcif-mipi-lvds1 {
		compatible = "rockchip,rkcif-mipi-lvds";
		rockchip,hw = <0x27>;
		iommus = <0x28>;
		status = "disabled";
		phandle = <0x2c>;
	};

	rkcif-mipi-lvds1-sditf {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2c>;
		status = "disabled";
	};

	rkcif-mipi-lvds1-sditf-vir1 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2c>;
		status = "disabled";
	};

	rkcif-mipi-lvds1-sditf-vir2 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2c>;
		status = "disabled";
	};

	rkcif-mipi-lvds1-sditf-vir3 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2c>;
		status = "disabled";
	};

	rkcif-mipi-lvds2 {
		compatible = "rockchip,rkcif-mipi-lvds";
		rockchip,hw = <0x27>;
		iommus = <0x28>;
		status = "disabled";
		phandle = <0x2d>;
	};

	rkcif-mipi-lvds2-sditf {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2d>;
		status = "disabled";
	};

	rkcif-mipi-lvds2-sditf-vir1 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2d>;
		status = "disabled";
	};

	rkcif-mipi-lvds2-sditf-vir2 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2d>;
		status = "disabled";
	};

	rkcif-mipi-lvds2-sditf-vir3 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2d>;
		status = "disabled";
	};

	rkcif-mipi-lvds3 {
		compatible = "rockchip,rkcif-mipi-lvds";
		rockchip,hw = <0x27>;
		iommus = <0x28>;
		status = "disabled";
		phandle = <0x2e>;
	};

	rkcif-mipi-lvds3-sditf {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2e>;
		status = "disabled";
	};

	rkcif-mipi-lvds3-sditf-vir1 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2e>;
		status = "disabled";
	};

	rkcif-mipi-lvds3-sditf-vir2 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2e>;
		status = "disabled";
	};

	rkcif-mipi-lvds3-sditf-vir3 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <0x2e>;
		status = "disabled";
	};

	rkisp-vir0 {
		compatible = "rockchip,rkisp-vir";
		rockchip,hw = <0x2f>;
		status = "okay";

		port {
			#address-cells = <0x01>;
			#size-cells = <0x00>;

			endpoint@0 {
				reg = <0x00>;
				remote-endpoint = <0x30>;
				phandle = <0x2b>;
			};
		};
	};

	rkisp-vir1 {
		compatible = "rockchip,rkisp-vir";
		rockchip,hw = <0x2f>;
		status = "disabled";
	};

	rkisp-vir2 {
		compatible = "rockchip,rkisp-vir";
		rockchip,hw = <0x2f>;
		status = "disabled";
	};

	rkisp-vir3 {
		compatible = "rockchip,rkisp-vir";
		rockchip,hw = <0x2f>;
		status = "disabled";
	};

	rockchip-system-monitor {
		compatible = "rockchip,system-monitor";
		rockchip,thermal-zone = "soc-thermal";
	};

	thermal-zones {

		soc-thermal {
			polling-delay-passive = <0x14>;
			polling-delay = <0x3e8>;
			sustainable-power = <0x2ad>;
			thermal-sensors = <0x31 0x00>;

			trips {

				trip-point-0 {
					temperature = <0x124f8>;
					hysteresis = <0x7d0>;
					type = "passive";
				};

				trip-point-1 {
					temperature = <0x14c08>;
					hysteresis = <0x7d0>;
					type = "passive";
					phandle = <0x32>;
				};

				soc-crit {
					temperature = <0x1c138>;
					hysteresis = <0x7d0>;
					type = "critical";
				};
			};

			cooling-maps {

				map0 {
					trip = <0x32>;
					cooling-device = <0x0e 0xffffffff 0xffffffff>;
					contribution = <0x400>;
				};

				map1 {
					trip = <0x32>;
					cooling-device = <0x33 0xffffffff 0xffffffff>;
					contribution = <0x400>;
				};
			};
		};
	};

	timer {
		compatible = "arm,armv8-timer";
		interrupts = <0x01 0x0d 0xf08 0x01 0x0e 0xf08 0x01 0x0b 0xf08 0x01 0x0a 0xf08>;
	};

	vendor-storage {
		compatible = "rockchip,ram-vendor-storage";
		memory-region = <0x34>;
		status = "okay";
	};

	scmi-shmem@10f000 {
		compatible = "arm,scmi-shmem";
		reg = <0x00 0x10f000 0x00 0x100>;
		phandle = <0x26>;
	};

	usbdrd {
		compatible = "rockchip,rk3562-dwc3\0rockchip,rk3399-dwc3";
		clocks = <0x02 0x10e 0x02 0x10d 0x02 0x10c 0x02 0x106>;
		clock-names = "ref\0suspend\0bus\0pipe_clk";
		#address-cells = <0x02>;
		#size-cells = <0x02>;
		ranges;
		status = "okay";

		usb@fe500000 {
			compatible = "snps,dwc3";
			reg = <0x00 0xfe500000 0x00 0x400000>;
			interrupts = <0x00 0x95 0x04>;
			dr_mode = "otg";
			phys = <0x35>;
			phy-names = "usb2-phy";
			phy_type = "utmi_wide";
			power-domains = <0x36 0x0f>;
			resets = <0x02 0x10a>;
			reset-names = "usb3-otg";
			snps,dis_enblslpm_quirk;
			snps,dis-u1-entry-quirk;
			snps,dis-u2-entry-quirk;
			snps,dis-u2-freeclk-exists-quirk;
			snps,dis-del-phy-power-chg-quirk;
			snps,dis-tx-ipgap-linecheck-quirk;
			snps,dis_rxdet_inp3_quirk;
			quirk-skip-phy-init;
			status = "okay";
			extcon = <0x37>;
			maximum-speed = "high-speed";
			snps,dis_u2_susphy_quirk;
			snps,usb2-lpm-disable;
		};
	};

	interrupt-controller@fe901000 {
		compatible = "arm,gic-400";
		#interrupt-cells = <0x03>;
		#address-cells = <0x02>;
		interrupt-controller;
		reg = <0x00 0xfe901000 0x00 0x1000 0x00 0xfe902000 0x00 0x2000 0x00 0xfe904000 0x00 0x2000 0x00 0xfe906000 0x00 0x2000>;
		interrupts = <0x01 0x09 0xf08>;
		phandle = <0x01>;
	};

	usb@fed00000 {
		compatible = "generic-ehci";
		reg = <0x00 0xfed00000 0x00 0x40000>;
		interrupts = <0x00 0x96 0x04>;
		clocks = <0x02 0x9e 0x02 0x9f 0x37>;
		clock-names = "usbhost\0arbiter\0utmi";
		phys = <0x38>;
		phy-names = "usb2-phy";
		status = "okay";
	};

	usb@fed40000 {
		compatible = "generic-ohci";
		reg = <0x00 0xfed40000 0x00 0x40000>;
		interrupts = <0x00 0x97 0x04>;
		clocks = <0x02 0x9e 0x02 0x9f 0x37>;
		clock-names = "usbhost\0arbiter\0utmi";
		phys = <0x38>;
		phy-names = "usb2-phy";
		status = "okay";
	};

	debug@fed90000 {
		compatible = "rockchip,debug";
		reg = <0x00 0xfed90000 0x00 0x2000 0x00 0xfed92000 0x00 0x2000 0x00 0xfed94000 0x00 0x2000 0x00 0xfed96000 0x00 0x2000>;
	};

	qos@fee03800 {
		compatible = "syscon";
		reg = <0x00 0xfee03800 0x00 0x20>;
	};

	qos@fee10000 {
		compatible = "syscon";
		reg = <0x00 0xfee10000 0x00 0x20>;
	};

	qos@fee10100 {
		compatible = "syscon";
		reg = <0x00 0xfee10100 0x00 0x20>;
	};

	qos@fee10200 {
		compatible = "syscon";
		reg = <0x00 0xfee10200 0x00 0x20>;
	};

	qos@fee10300 {
		compatible = "syscon";
		reg = <0x00 0xfee10300 0x00 0x20>;
	};

	qos@fee10400 {
		compatible = "syscon";
		reg = <0x00 0xfee10400 0x00 0x20>;
	};

	qos@fee20000 {
		compatible = "syscon";
		reg = <0x00 0xfee20000 0x00 0x20>;
	};

	qos@fee20100 {
		compatible = "syscon";
		reg = <0x00 0xfee20100 0x00 0x20>;
	};

	qos@fee30000 {
		compatible = "syscon";
		reg = <0x00 0xfee30000 0x00 0x20>;
		priority-init = <0x202>;
		phandle = <0x4f>;
	};

	qos@fee40000 {
		compatible = "syscon";
		reg = <0x00 0xfee40000 0x00 0x20>;
		phandle = <0x50>;
	};

	qos@fee50000 {
		compatible = "syscon";
		reg = <0x00 0xfee50000 0x00 0x20>;
		phandle = <0x51>;
	};

	qos@fee60000 {
		compatible = "syscon";
		reg = <0x00 0xfee60000 0x00 0x20>;
		phandle = <0x54>;
	};

	qos@fee70000 {
		compatible = "syscon";
		reg = <0x00 0xfee70000 0x00 0x20>;
		phandle = <0x52>;
	};

	qos@fee70100 {
		compatible = "syscon";
		reg = <0x00 0xfee70100 0x00 0x20>;
		phandle = <0x53>;
	};

	qos@fee80000 {
		compatible = "syscon";
		reg = <0x00 0xfee80000 0x00 0x20>;
		phandle = <0x55>;
	};

	qos@fee90000 {
		compatible = "syscon";
		reg = <0x00 0xfee90000 0x00 0x20>;
		phandle = <0x58>;
	};

	qos@fee90100 {
		compatible = "syscon";
		reg = <0x00 0xfee90100 0x00 0x20>;
		phandle = <0x56>;
	};

	qos@fee90200 {
		compatible = "syscon";
		reg = <0x00 0xfee90200 0x00 0x20>;
		phandle = <0x57>;
	};

	qos@feea0000 {
		compatible = "syscon";
		reg = <0x00 0xfeea0000 0x00 0x20>;
		phandle = <0x59>;
	};

	qos@feea0100 {
		compatible = "syscon";
		reg = <0x00 0xfeea0100 0x00 0x20>;
		phandle = <0x5a>;
	};

	qos@feeb0000 {
		compatible = "syscon";
		reg = <0x00 0xfeeb0000 0x00 0x20>;
	};

	qos@feeb0100 {
		compatible = "syscon";
		reg = <0x00 0xfeeb0100 0x00 0x20>;
	};

	qos@feeb0200 {
		compatible = "syscon";
		reg = <0x00 0xfeeb0200 0x00 0x20>;
	};

	qos@feeb0300 {
		compatible = "syscon";
		reg = <0x00 0xfeeb0300 0x00 0x20>;
	};

	qos@feeb0400 {
		compatible = "syscon";
		reg = <0x00 0xfeeb0400 0x00 0x20>;
	};

	qos@feeb0500 {
		compatible = "syscon";
		reg = <0x00 0xfeeb0500 0x00 0x20>;
	};

	qos@feeb0600 {
		compatible = "syscon";
		reg = <0x00 0xfeeb0600 0x00 0x20>;
	};

	qos@feeb0700 {
		compatible = "syscon";
		reg = <0x00 0xfeeb0700 0x00 0x20>;
	};

	qos@feeb0800 {
		compatible = "syscon";
		reg = <0x00 0xfeeb0800 0x00 0x20>;
	};

	syscon@ff010000 {
		compatible = "rockchip,rk3562-pmu-grf\0syscon\0simple-mfd";
		reg = <0x00 0xff010000 0x00 0x10000>;
		phandle = <0x73>;

		reboot-mode {
			compatible = "syscon-reboot-mode";
			offset = <0x220>;
			mode-bootloader = <0x5242c301>;
			mode-charge = <0x5242c30b>;
			mode-fastboot = <0x5242c309>;
			mode-loader = <0x5242c301>;
			mode-normal = <0x5242c300>;
			mode-recovery = <0x5242c303>;
			mode-ums = <0x5242c30c>;
			mode-panic = <0x5242c307>;
			mode-watchdog = <0x5242c308>;
		};
	};

	syscon@ff030000 {
		compatible = "rockchip,rk3562-sys-grf\0syscon\0simple-mfd";
		reg = <0x00 0xff030000 0x00 0x10000>;
		phandle = <0x0d>;

		lvds {
			compatible = "rockchip,rk3562-lvds";
			phys = <0x39>;
			phy-names = "phy";
			status = "okay";

			ports {
				#address-cells = <0x01>;
				#size-cells = <0x00>;

				port@0 {
					reg = <0x00>;
					#address-cells = <0x01>;
					#size-cells = <0x00>;

					endpoint@0 {
						reg = <0x00>;
						remote-endpoint = <0x1e>;
						status = "okay";
						phandle = <0x70>;
					};
				};

				port@1 {
					reg = <0x01>;

					endpoint {
						remote-endpoint = <0x3a>;
						phandle = <0xd5>;
					};
				};
			};
		};
	};

	syscon@ff040000 {
		compatible = "rockchip,rk3562-peri-grf\0syscon";
		reg = <0x00 0xff040000 0x00 0x10000>;
		phandle = <0x8c>;
	};

	syscon@ff060000 {
		compatible = "rockchip,rk3562-ioc-grf\0syscon\0simple-mfd";
		reg = <0x00 0xff060000 0x00 0x30000>;
		phandle = <0x6d>;

		rgb {
			compatible = "rockchip,rk3562-rgb";
			pinctrl-names = "default";
			pinctrl-0 = <0x3b>;
			status = "disabled";

			ports {
				#address-cells = <0x01>;
				#size-cells = <0x00>;

				port@0 {
					reg = <0x00>;
					#address-cells = <0x01>;
					#size-cells = <0x00>;

					endpoint@0 {
						reg = <0x00>;
						remote-endpoint = <0x1f>;
						status = "disabled";
						phandle = <0x6e>;
					};
				};
			};
		};
	};

	syscon@ff090000 {
		compatible = "rockchip,rk3562-usbphy-grf\0syscon";
		reg = <0x00 0xff090000 0x00 0x8000>;
		phandle = <0x8a>;
	};

	syscon@ff098000 {
		compatible = "rockchip,rk3562-pipephy-grf\0syscon";
		reg = <0x00 0xff098000 0x00 0x8000>;
		phandle = <0x8d>;
	};

	clock-controller@ff100000 {
		compatible = "rockchip,rk3562-cru";
		reg = <0x00 0xff100000 0x00 0x40000>;
		rockchip,grf = <0x0d>;
		#clock-cells = <0x01>;
		#reset-cells = <0x01>;
		assigned-clocks = <0x02 0x02 0x02 0x05>;
		assigned-clock-rates = <0x46cf7100 0x3b9aca00>;
		phandle = <0x02>;
	};

	i2c@ff200000 {
		compatible = "rockchip,rk3562-i2c\0rockchip,rk3399-i2c";
		reg = <0x00 0xff200000 0x00 0x1000>;
		clocks = <0x02 0x126 0x02 0x125>;
		clock-names = "i2c\0pclk";
		interrupts = <0x00 0x0c 0x04>;
		pinctrl-names = "default";
		pinctrl-0 = <0x3c>;
		#address-cells = <0x01>;
		#size-cells = <0x00>;
		status = "okay";

		pmic@20 {
			compatible = "rockchip,rk809";
			reg = <0x20>;
			interrupt-parent = <0x3d>;
			interrupts = <0x03 0x08>;
			pinctrl-names = "default\0pmic-sleep\0pmic-power-off\0pmic-reset";
			pinctrl-0 = <0x3e>;
			pinctrl-1 = <0x3f 0x40>;
			pinctrl-2 = <0x41 0x42>;
			pinctrl-3 = <0x41 0x43>;
			rockchip,system-power-controller;
			wakeup-source;
			#clock-cells = <0x01>;
			clock-output-names = "rk808-clkout1\0rk808-clkout2";
			pmic-reset-func = <0x00>;
			not-save-power-en = <0x01>;
			vcc1-supply = <0x44>;
			vcc2-supply = <0x44>;
			vcc3-supply = <0x44>;
			vcc4-supply = <0x44>;
			vcc5-supply = <0x44>;
			vcc6-supply = <0x44>;
			vcc7-supply = <0x44>;
			vcc8-supply = <0x44>;
			vcc9-supply = <0x44>;

			pwrkey {
				status = "okay";
			};

			pinctrl_rk8xx {
				gpio-controller;
				#gpio-cells = <0x02>;

				rk817_slppin_null {
					pins = "gpio_slp";
					function = "pin_fun0";
				};

				rk817_slppin_slp {
					pins = "gpio_slp";
					function = "pin_fun1";
					phandle = <0x40>;
				};

				rk817_slppin_pwrdn {
					pins = "gpio_slp";
					function = "pin_fun2";
					phandle = <0x42>;
				};

				rk817_slppin_rst {
					pins = "gpio_slp";
					function = "pin_fun3";
					phandle = <0x43>;
				};
			};

			regulators {

				DCDC_REG1 {
					regulator-always-on;
					regulator-boot-on;
					regulator-min-microvolt = <0x7a120>;
					regulator-max-microvolt = <0x149970>;
					regulator-init-microvolt = <0xdbba0>;
					regulator-ramp-delay = <0x1771>;
					regulator-initial-mode = <0x02>;
					regulator-name = "vdd_logic";
					phandle = <0x22>;

					regulator-state-mem {
						regulator-off-in-suspend;
					};
				};

				DCDC_REG2 {
					regulator-always-on;
					regulator-boot-on;
					regulator-min-microvolt = <0x7a120>;
					regulator-max-microvolt = <0x149970>;
					regulator-init-microvolt = <0xdbba0>;
					regulator-ramp-delay = <0x1771>;
					regulator-initial-mode = <0x02>;
					regulator-name = "vdd_cpu";
					phandle = <0x08>;

					regulator-state-mem {
						regulator-off-in-suspend;
					};
				};

				DCDC_REG3 {
					regulator-always-on;
					regulator-boot-on;
					regulator-initial-mode = <0x02>;
					regulator-name = "vcc_ddr";

					regulator-state-mem {
						regulator-on-in-suspend;
					};
				};

				DCDC_REG4 {
					regulator-always-on;
					regulator-boot-on;
					regulator-min-microvolt = <0x7a120>;
					regulator-max-microvolt = <0x149970>;
					regulator-init-microvolt = <0xdbba0>;
					regulator-ramp-delay = <0x1771>;
					regulator-initial-mode = <0x02>;
					regulator-name = "vdd_gpu";
					phandle = <0x62>;

					regulator-state-mem {
						regulator-off-in-suspend;
					};
				};

				LDO_REG1 {
					regulator-min-microvolt = <0x2ab980>;
					regulator-max-microvolt = <0x2ab980>;
					regulator-name = "vcc2v8_dvp";

					regulator-state-mem {
						regulator-off-in-suspend;
					};
				};

				LDO_REG2 {
					regulator-always-on;
					regulator-boot-on;
					regulator-min-microvolt = <0xdbba0>;
					regulator-max-microvolt = <0xdbba0>;
					regulator-name = "vdda_0v9";

					regulator-state-mem {
						regulator-off-in-suspend;
					};
				};

				LDO_REG3 {
					regulator-always-on;
					regulator-boot-on;
					regulator-min-microvolt = <0xdbba0>;
					regulator-max-microvolt = <0xdbba0>;
					regulator-name = "vdda0v9_pmu";

					regulator-state-mem {
						regulator-on-in-suspend;
						regulator-suspend-microvolt = <0xdbba0>;
					};
				};

				LDO_REG4 {
					regulator-always-on;
					regulator-boot-on;
					regulator-min-microvolt = <0x2dc6c0>;
					regulator-max-microvolt = <0x2dc6c0>;
					regulator-name = "vccio_acodec";

					regulator-state-mem {
						regulator-off-in-suspend;
					};
				};

				LDO_REG5 {
					regulator-always-on;
					regulator-boot-on;
					regulator-min-microvolt = <0x1b7740>;
					regulator-max-microvolt = <0x325aa0>;
					regulator-name = "vccio_sd";
					phandle = <0xa9>;

					regulator-state-mem {
						regulator-off-in-suspend;
					};
				};

				LDO_REG6 {
					regulator-always-on;
					regulator-boot-on;
					regulator-min-microvolt = <0x325aa0>;
					regulator-max-microvolt = <0x325aa0>;
					regulator-name = "vcc3v3_pmu";

					regulator-state-mem {
						regulator-on-in-suspend;
						regulator-suspend-microvolt = <0x325aa0>;
					};
				};

				LDO_REG7 {
					regulator-always-on;
					regulator-boot-on;
					regulator-min-microvolt = <0x1b7740>;
					regulator-max-microvolt = <0x1b7740>;
					regulator-name = "vcca_1v8";

					regulator-state-mem {
						regulator-off-in-suspend;
					};
				};

				LDO_REG8 {
					regulator-always-on;
					regulator-boot-on;
					regulator-min-microvolt = <0x1b7740>;
					regulator-max-microvolt = <0x1b7740>;
					regulator-name = "vcca1v8_pmu";

					regulator-state-mem {
						regulator-on-in-suspend;
						regulator-suspend-microvolt = <0x1b7740>;
					};
				};

				LDO_REG9 {
					regulator-min-microvolt = <0x1b7740>;
					regulator-max-microvolt = <0x1b7740>;
					regulator-name = "vcc1v8_dvp";

					regulator-state-mem {
						regulator-off-in-suspend;
					};
				};

				DCDC_REG5 {
					regulator-always-on;
					regulator-boot-on;
					regulator-min-microvolt = <0x1b7740>;
					regulator-max-microvolt = <0x1b7740>;
					regulator-name = "vcc_1v8";
					phandle = <0x89>;

					regulator-state-mem {
						regulator-off-in-suspend;
					};
				};

				SWITCH_REG1 {
					regulator-always-on;
					regulator-boot-on;
					regulator-name = "vcc_3v3";

					regulator-state-mem {
						regulator-off-in-suspend;
					};
				};

				SWITCH_REG2 {
					regulator-always-on;
					regulator-boot-on;
					regulator-name = "vcc3v3_sd";
					phandle = <0xa8>;

					regulator-state-mem {
						regulator-off-in-suspend;
					};
				};
			};

			codec {
				#sound-dai-cells = <0x01>;
				compatible = "rockchip,rk809-codec\0rockchip,rk817-codec";
				clocks = <0x45>;
				clock-names = "mclk";
				assigned-clocks = <0x45>;
				assigned-clock-rates = <0xbb8000>;
				pinctrl-names = "default";
				pinctrl-0 = <0x46>;
				hp-volume = <0x14>;
				spk-volume = <0x03>;
				mic-in-differential;
				status = "okay";
				phandle = <0xd8>;
			};
		};
	};

	serial@ff210000 {
		compatible = "rockchip,rk3562-uart\0snps,dw-apb-uart";
		reg = <0x00 0xff210000 0x00 0x100>;
		interrupts = <0x00 0x1e 0x04>;
		clocks = <0x02 0x12b 0x02 0x127>;
		clock-names = "baudclk\0apb_pclk";
		reg-shift = <0x02>;
		reg-io-width = <0x04>;
		dmas = <0x47 0x00>;
		status = "okay";
	};

	spi@ff220000 {
		compatible = "rockchip,rk3066-spi";
		reg = <0x00 0xff220000 0x00 0x1000>;
		interrupts = <0x00 0x34 0x04>;
		#address-cells = <0x01>;
		#size-cells = <0x00>;
		clocks = <0x02 0x12d 0x02 0x12c 0x02 0x12e>;
		clock-names = "spiclk\0apb_pclk\0sclk_in";
		dmas = <0x47 0x0d 0x47 0x0c>;
		dma-names = "tx\0rx";
		pinctrl-names = "default";
		pinctrl-0 = <0x48 0x49 0x4a>;
		num-cs = <0x02>;
		status = "disabled";
	};

	pwm@ff230000 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff230000 0x00 0x10>;
		interrupts = <0x00 0x14 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x4b>;
		clocks = <0x02 0x130 0x02 0x12f>;
		clock-names = "pwm\0pclk";
		status = "disabled";
	};

	pwm@ff230010 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff230010 0x00 0x10>;
		interrupts = <0x00 0x14 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x4c>;
		clocks = <0x02 0x130 0x02 0x12f>;
		clock-names = "pwm\0pclk";
		status = "disabled";
	};

	pwm@ff230020 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff230020 0x00 0x10>;
		interrupts = <0x00 0x14 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x4d>;
		clocks = <0x02 0x130 0x02 0x12f>;
		clock-names = "pwm\0pclk";
		status = "disabled";
	};

	pwm@ff230030 {
		compatible = "rockchip,remotectl-pwm";
		reg = <0x00 0xff230030 0x00 0x10>;
		interrupts = <0x00 0x14 0x04 0x00 0x15 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "default";
		pinctrl-0 = <0x4e>;
		clocks = <0x02 0x130 0x02 0x12f>;
		clock-names = "pwm\0pclk";
		status = "okay";
		assigned-clocks = <0x02 0x130>;
		assigned-clock-rates = <0x16e3600>;
		remote_pwm_id = <0x03>;
		handle_cpu_id = <0x01>;
		remote_support_psci = <0x01>;

		ir_key1 {
			rockchip,usercode = <0x4040>;
			rockchip,key_table = <0xf2 0xe8 0xba 0x9e 0xf4 0x67 0xf1 0x6c 0xef 0x69 0xee 0x6a 0xbd 0x66 0xea 0x73 0xe3 0x72 0xe2 0xd9 0xb2 0x74 0xbc 0x71 0xec 0x8b 0xbf 0x190 0xe0 0x191 0xe1 0x192 0xe9 0xb7 0xe6 0xf8 0xe8 0xb9 0xe7 0xba 0xf0 0x184 0xbe 0x175>;
		};

		ir_key2 {
			rockchip,usercode = <0xff00>;
			rockchip,key_table = <0xf9 0x66 0xbf 0x9e 0xfb 0x8b 0xaa 0xe8 0xb9 0x67 0xe9 0x6c 0xb8 0x69 0xea 0x6a 0xeb 0x72 0xef 0x73 0xf7 0x71 0xe7 0x74 0xfc 0x74 0xa9 0x72 0xa8 0x72 0xe0 0x72 0xa5 0x72 0xab 0xb7 0xb7 0x184 0xe8 0x184 0xf8 0xb8 0xaf 0xb9 0xed 0x72 0xee 0xba 0xb3 0x72 0xf1 0x72 0xf2 0x72 0xf3 0xd9 0xb4 0x72 0xbe 0xd9>;
		};

		ir_key3 {
			rockchip,usercode = <0x1dcc>;
			rockchip,key_table = <0xee 0xe8 0xf0 0x9e 0xf8 0x67 0xbb 0x6c 0xef 0x69 0xed 0x6a 0xfc 0x66 0xf1 0x73 0xfd 0x72 0xb7 0xd9 0xff 0x74 0xf3 0x71 0xbf 0x8b 0xf9 0x191 0xf5 0x192 0xb3 0x184 0xbe 0x02 0xba 0x03 0xb2 0x04 0xbd 0x05 0xf9 0x06 0xb1 0x07 0xfc 0x08 0xf8 0x09 0xb0 0x0a 0xb6 0x0b 0xb5 0x0e>;
		};
	};

	power-management@ff258000 {
		compatible = "rockchip,rk3562-pmu\0syscon\0simple-mfd";
		reg = <0x00 0xff258000 0x00 0x1000>;

		power-controller {
			compatible = "rockchip,rk3562-power-controller";
			#power-domain-cells = <0x01>;
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			status = "okay";
			phandle = <0x36>;

			pd_gpu@8 {
				reg = <0x08>;
				pm_qos = <0x4f>;
			};

			pd_npu@7 {
				reg = <0x07>;
				pm_qos = <0x50>;
			};

			pd_vdpu@11 {
				reg = <0x0b>;
				pm_qos = <0x51>;
			};

			pd_vi@12 {
				reg = <0x0c>;
				#address-cells = <0x01>;
				#size-cells = <0x00>;
				pm_qos = <0x52 0x53>;

				pd_vepu@10 {
					reg = <0x0a>;
					pm_qos = <0x54>;
				};
			};

			pd_vo@13 {
				reg = <0x0d>;
				#address-cells = <0x01>;
				#size-cells = <0x00>;
				pm_qos = <0x55>;

				pd_rga@14 {
					reg = <0x0e>;
					pm_qos = <0x56 0x57 0x58>;
				};
			};

			pd_php@15 {
				reg = <0x0f>;
				pm_qos = <0x59 0x5a>;
			};
		};
	};

	mailbox@ff290000 {
		compatible = "rockchip,rk3562-mailbox\0rockchip,rk3368-mailbox";
		reg = <0x00 0xff290000 0x00 0x200>;
		interrupts = <0x00 0x12 0x04>;
		clocks = <0x02 0x137>;
		clock-names = "pclk_mailbox";
		#mbox-cells = <0x01>;
		status = "disabled";
	};

	npu@ff300000 {
		compatible = "rockchip,rk3562-rknpu";
		reg = <0x00 0xff300000 0x00 0x10000>;
		interrupts = <0x00 0x77 0x04>;
		clocks = <0x05 0x0a 0x02 0x0a 0x02 0x6f>;
		clock-names = "scmi_clk\0aclk\0hclk";
		assigned-clocks = <0x02 0x0a>;
		assigned-clock-rates = <0x23c34600>;
		resets = <0x02 0x64 0x02 0x65>;
		reset-names = "srst_a\0srst_h";
		power-domains = <0x36 0x07>;
		operating-points-v2 = <0x5b>;
		iommus = <0x5c>;
		status = "okay";
		rknpu-supply = <0x5d>;
	};

	npu-opp-table {
		compatible = "operating-points-v2";
		mbist-vmin = <0xc96a8 0xdbba0 0xee098>;
		nvmem-cells = <0x5e 0x5f 0x0b 0x60>;
		nvmem-cell-names = "leakage\0opp-info\0mbist-vmin\0pvtm";
		rockchip,pvtm-voltage-sel = <0x00 0x2f8 0x00 0x2f9 0x320 0x01 0x321 0x348 0x02 0x349 0x370 0x03 0x371 0x270f 0x04>;
		rockchip,pvtm-pvtpll;
		rockchip,pvtm-offset = <0x674>;
		rockchip,pvtm-sample-time = <0x44c>;
		rockchip,pvtm-freq = <0xdbba0>;
		rockchip,pvtm-volt = <0xdbba0>;
		rockchip,pvtm-ref-temp = <0x28>;
		rockchip,pvtm-temp-prop = <0x00 0x00>;
		rockchip,pvtm-thermal-zone = "soc-thermal";
		rockchip,grf = <0x0d>;
		rockchip,temp-hysteresis = <0x1388>;
		rockchip,low-temp = <0x2710>;
		rockchip,low-temp-min-volt = <0xe1d48>;
		phandle = <0x5b>;

		opp-300000000 {
			opp-hz = <0x00 0x11e1a300>;
			opp-microvolt = <0xc96a8 0xc96a8 0xf4240>;
		};

		opp-400000000 {
			opp-hz = <0x00 0x17d78400>;
			opp-microvolt = <0xc96a8 0xc96a8 0xf4240>;
		};

		opp-500000000 {
			opp-hz = <0x00 0x1dcd6500>;
			opp-microvolt = <0xc96a8 0xc96a8 0xf4240>;
		};

		opp-600000000 {
			opp-hz = <0x00 0x23c34600>;
			opp-microvolt = <0xd59f8 0xd59f8 0xf4240>;
			opp-microvolt-L0 = <0xd59f8 0xd59f8 0xf4240>;
			opp-microvolt-L1 = <0xcf850 0xcf850 0xf4240>;
			opp-microvolt-L2 = <0xc96a8 0xc96a8 0xf4240>;
			opp-microvolt-L3 = <0xc96a8 0xc96a8 0xf4240>;
			opp-microvolt-L4 = <0xc96a8 0xc96a8 0xf4240>;
		};

		opp-700000000 {
			opp-hz = <0x00 0x29b92700>;
			opp-microvolt = <0xe1d48 0xe1d48 0xf4240>;
			opp-microvolt-L0 = <0xe1d48 0xe1d48 0xf4240>;
			opp-microvolt-L1 = <0xdbba0 0xdbba0 0xf4240>;
			opp-microvolt-L2 = <0xd59f8 0xd59f8 0xf4240>;
			opp-microvolt-L3 = <0xcf850 0xcf850 0xf4240>;
			opp-microvolt-L4 = <0xc96a8 0xc96a8 0xf4240>;
		};

		opp-800000000 {
			opp-hz = <0x00 0x2faf0800>;
			opp-microvolt = <0xee098 0xee098 0xf4240>;
			opp-microvolt-L0 = <0xee098 0xee098 0xf4240>;
			opp-microvolt-L1 = <0xe7ef0 0xe7ef0 0xf4240>;
			opp-microvolt-L2 = <0xe1d48 0xe1d48 0xf4240>;
			opp-microvolt-L3 = <0xdbba0 0xdbba0 0xf4240>;
			opp-microvolt-L4 = <0xd59f8 0xd59f8 0xf4240>;
		};

		opp-900000000 {
			opp-hz = <0x00 0x35a4e900>;
			opp-microvolt = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L0 = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L1 = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L2 = <0xee098 0xee098 0xf4240>;
			opp-microvolt-L3 = <0xe7ef0 0xe7ef0 0xf4240>;
			opp-microvolt-L4 = <0xe1d48 0xe1d48 0xf4240>;
		};

		opp-1000000000 {
			opp-hz = <0x00 0x3b9aca00>;
			opp-microvolt = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L0 = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L1 = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L2 = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L3 = <0xee098 0xee098 0xf4240>;
			opp-microvolt-L4 = <0xe7ef0 0xe7ef0 0xf4240>;
		};
	};

	iommu@ff30a000 {
		compatible = "rockchip,iommu-v2";
		reg = <0x00 0xff30a000 0x00 0x40>;
		interrupts = <0x00 0x77 0x04>;
		interrupt-names = "rknpu_mmu";
		clocks = <0x02 0x0a 0x02 0x6f>;
		clock-names = "aclk\0iface";
		power-domains = <0x36 0x07>;
		#iommu-cells = <0x00>;
		status = "okay";
		phandle = <0x5c>;
	};

	gpu@ff320000 {
		compatible = "arm,mali-bifrost";
		reg = <0x00 0xff320000 0x00 0x4000>;
		interrupts = <0x00 0x4b 0x04 0x00 0x4d 0x04 0x00 0x4c 0x04>;
		interrupt-names = "GPU\0MMU\0JOB";
		upthreshold = <0x28>;
		downdifferential = <0x0a>;
		clocks = <0x05 0x09 0x02 0x09 0x02 0x6b 0x02 0x69>;
		clock-names = "clk_mali\0clk_gpu\0clk_gpu_brg\0aclk_gpu";
		power-domains = <0x36 0x08>;
		operating-points-v2 = <0x61>;
		#cooling-cells = <0x02>;
		dynamic-power-coefficient = <0x334>;
		status = "okay";
		mali-supply = <0x62>;
		phandle = <0x33>;
	};

	gpu-opp-table {
		compatible = "operating-points-v2";
		mbist-vmin = <0xc96a8 0xdbba0 0xee098>;
		nvmem-cells = <0x63 0x64 0x0b 0x65>;
		nvmem-cell-names = "leakage\0opp-info\0mbist-vmin\0pvtm";
		rockchip,pvtm-voltage-sel = <0x00 0x30c 0x00 0x30d 0x334 0x01 0x335 0x35c 0x02 0x35d 0x384 0x03 0x385 0x270f 0x04>;
		rockchip,pvtm-pvtpll;
		rockchip,pvtm-offset = <0x654>;
		rockchip,pvtm-sample-time = <0x44c>;
		rockchip,pvtm-freq = <0xdbba0>;
		rockchip,pvtm-volt = <0xdbba0>;
		rockchip,pvtm-ref-temp = <0x28>;
		rockchip,pvtm-temp-prop = <0x00 0x00>;
		rockchip,pvtm-thermal-zone = "soc-thermal";
		rockchip,grf = <0x0d>;
		rockchip,temp-hysteresis = <0x1388>;
		rockchip,low-temp = <0x2710>;
		rockchip,low-temp-min-volt = <0xe1d48>;
		phandle = <0x61>;

		opp-300000000 {
			opp-hz = <0x00 0x11e1a300>;
			opp-microvolt = <0xc96a8 0xc96a8 0xf4240>;
		};

		opp-400000000 {
			opp-hz = <0x00 0x17d78400>;
			opp-microvolt = <0xc96a8 0xc96a8 0xf4240>;
		};

		opp-500000000 {
			opp-hz = <0x00 0x1dcd6500>;
			opp-microvolt = <0xc96a8 0xc96a8 0xf4240>;
		};

		opp-600000000 {
			opp-hz = <0x00 0x23c34600>;
			opp-microvolt = <0xc96a8 0xc96a8 0xf4240>;
		};

		opp-700000000 {
			opp-hz = <0x00 0x29b92700>;
			opp-microvolt = <0xdbba0 0xdbba0 0xf4240>;
			opp-microvolt-L0 = <0xdbba0 0xdbba0 0xf4240>;
			opp-microvolt-L1 = <0xd59f8 0xd59f8 0xf4240>;
			opp-microvolt-L2 = <0xcf850 0xcf850 0xf4240>;
			opp-microvolt-L3 = <0xc96a8 0xc96a8 0xf4240>;
			opp-microvolt-L4 = <0xc96a8 0xc96a8 0xf4240>;
		};

		opp-800000000 {
			opp-hz = <0x00 0x2faf0800>;
			opp-microvolt = <0xe7ef0 0xe7ef0 0xf4240>;
			opp-microvolt-L0 = <0xe7ef0 0xe7ef0 0xf4240>;
			opp-microvolt-L1 = <0xe1d48 0xe1d48 0xf4240>;
			opp-microvolt-L2 = <0xdbba0 0xdbba0 0xf4240>;
			opp-microvolt-L3 = <0xd59f8 0xd59f8 0xf4240>;
			opp-microvolt-L4 = <0xcf850 0xcf850 0xf4240>;
		};

		opp-900000000 {
			opp-hz = <0x00 0x35a4e900>;
			opp-microvolt = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L0 = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L1 = <0xee098 0xee098 0xf4240>;
			opp-microvolt-L2 = <0xe7ef0 0xe7ef0 0xf4240>;
			opp-microvolt-L3 = <0xe1d48 0xe1d48 0xf4240>;
			opp-microvolt-L4 = <0xdbba0 0xdbba0 0xf4240>;
		};

		opp-j-od-800000000 {
			opp-supported-hw = <0x04 0xffff>;
			opp-hz = <0x00 0x2faf0800>;
			opp-microvolt = <0xe7ef0 0xe7ef0 0xf4240>;
			opp-microvolt-L0 = <0xe7ef0 0xe7ef0 0xf4240>;
			opp-microvolt-L1 = <0xe1d48 0xe1d48 0xf4240>;
			opp-microvolt-L2 = <0xdbba0 0xdbba0 0xf4240>;
			opp-microvolt-L3 = <0xd59f8 0xd59f8 0xf4240>;
			opp-microvolt-L4 = <0xcf850 0xcf850 0xf4240>;
			status = "disabled";
		};

		opp-j-od-900000000 {
			opp-supported-hw = <0x04 0xffff>;
			opp-hz = <0x00 0x35a4e900>;
			opp-microvolt = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L0 = <0xf4240 0xf4240 0xf4240>;
			opp-microvolt-L1 = <0xee098 0xee098 0xf4240>;
			opp-microvolt-L2 = <0xe7ef0 0xe7ef0 0xf4240>;
			opp-microvolt-L3 = <0xe1d48 0xe1d48 0xf4240>;
			opp-microvolt-L4 = <0xdbba0 0xdbba0 0xf4240>;
			status = "disabled";
		};
	};

	rkvdec@ff340100 {
		compatible = "rockchip,rkv-decoder-rk3562\0rockchip,rkv-decoder-v2";
		reg = <0x00 0xff340100 0x00 0x400 0x00 0xff340000 0x00 0x100>;
		reg-names = "regs\0link";
		interrupts = <0x00 0x7a 0x04>;
		interrupt-names = "irq_dec";
		clocks = <0x02 0x14a 0x02 0x14b 0x02 0x148>;
		clock-names = "aclk_vcodec\0hclk_vcodec\0clk_hevc_cabac";
		rockchip,normal-rates = <0xbcd3d80 0x00 0x179a7b00>;
		assigned-clocks = <0x02 0x14a 0x02 0x148>;
		assigned-clock-rates = <0xbcd3d80 0x179a7b00>;
		resets = <0x02 0xa7 0x02 0xa8 0x02 0xa2>;
		reset-names = "video_a\0video_h\0video_hevc_cabac";
		power-domains = <0x36 0x0b>;
		iommus = <0x66>;
		rockchip,srv = <0x67>;
		rockchip,taskqueue-node = <0x00>;
		rockchip,resetgroup-node = <0x00>;
		rockchip,task-capacity = <0x10>;
		status = "okay";
	};

	iommu@ff340800 {
		compatible = "rockchip,iommu-v2";
		reg = <0x00 0xff340800 0x00 0x40 0x00 0xff340900 0x00 0x40>;
		interrupts = <0x00 0x7b 0x04>;
		interrupt-names = "rkvdec_mmu";
		clocks = <0x02 0x14a 0x02 0x14b 0x02 0x148>;
		clock-names = "aclk\0iface\0clk_hevc_cabac";
		power-domains = <0x36 0x0b>;
		rockchip,shootdown-entire;
		#iommu-cells = <0x00>;
		status = "okay";
		phandle = <0x66>;
	};

	rkvenc@ff360000 {
		compatible = "rockchip,rkv-encoder-rk3562\0rockchip,rkv-encoder-v2";
		reg = <0x00 0xff360000 0x00 0x6000>;
		interrupts = <0x00 0x88 0x04>;
		interrupt-names = "irq_rkvenc";
		clocks = <0x02 0x14f 0x02 0x150 0x02 0x14c>;
		clock-names = "aclk_vcodec\0hclk_vcodec\0clk_core";
		rockchip,normal-rates = <0x11b3dc40 0x00 0x11b3dc40>;
		resets = <0x02 0x95 0x02 0x96 0x02 0x90>;
		reset-names = "video_a\0video_h\0video_core";
		assigned-clocks = <0x02 0x14f 0x02 0x14c>;
		assigned-clock-rates = <0x11b3dc40 0x11b3dc40>;
		power-domains = <0x36 0x0a>;
		iommus = <0x68>;
		rockchip,srv = <0x67>;
		rockchip,taskqueue-node = <0x01>;
		rockchip,resetgroup-node = <0x01>;
		status = "okay";
	};

	iommu@ff36f000 {
		compatible = "rockchip,iommu-v2";
		reg = <0x00 0xff36f000 0x00 0x40>;
		interrupts = <0x00 0x89 0x04>;
		interrupt-names = "rkvenc_mmu";
		clocks = <0x02 0x14f 0x02 0x150>;
		clock-names = "aclk\0iface";
		power-domains = <0x36 0x0a>;
		rockchip,shootdown-entire;
		#iommu-cells = <0x00>;
		status = "okay";
		phandle = <0x68>;
	};

	mipi0-csi2@ff380000 {
		compatible = "rockchip,rk3562-mipi-csi2";
		reg = <0x00 0xff380000 0x00 0x10000>;
		reg-names = "csihost_regs";
		interrupts = <0x00 0x52 0x04 0x00 0x53 0x04>;
		interrupt-names = "csi-intr1\0csi-intr2";
		clocks = <0x02 0x15e>;
		clock-names = "pclk_csi2host";
		resets = <0x02 0xc0>;
		reset-names = "srst_csihost_p";
		status = "okay";

		ports {
			#address-cells = <0x01>;
			#size-cells = <0x00>;

			port@0 {
				reg = <0x00>;
				#address-cells = <0x01>;
				#size-cells = <0x00>;

				endpoint@1 {
					reg = <0x01>;
					remote-endpoint = <0x69>;
					phandle = <0x18>;
				};
			};

			port@1 {
				reg = <0x01>;
				#address-cells = <0x01>;
				#size-cells = <0x00>;

				endpoint@0 {
					reg = <0x00>;
					remote-endpoint = <0x6a>;
					phandle = <0x29>;
				};
			};
		};
	};

	mipi1-csi2@ff390000 {
		compatible = "rockchip,rk3562-mipi-csi2";
		reg = <0x00 0xff390000 0x00 0x10000>;
		reg-names = "csihost_regs";
		interrupts = <0x00 0x54 0x04 0x00 0x55 0x04>;
		interrupt-names = "csi-intr1\0csi-intr2";
		clocks = <0x02 0x15f>;
		clock-names = "pclk_csi2host";
		resets = <0x02 0xc1>;
		reset-names = "srst_csihost_p";
		status = "disabled";
	};

	mipi2-csi2@ff3a0000 {
		compatible = "rockchip,rk3562-mipi-csi2";
		reg = <0x00 0xff3a0000 0x00 0x10000>;
		reg-names = "csihost_regs";
		interrupts = <0x00 0x56 0x04 0x00 0x57 0x04>;
		interrupt-names = "csi-intr1\0csi-intr2";
		clocks = <0x02 0x160>;
		clock-names = "pclk_csi2host";
		resets = <0x02 0xc2>;
		reset-names = "srst_csihost_p";
		status = "disabled";
	};

	mipi3-csi2@ff3b0000 {
		compatible = "rockchip,rk3562-mipi-csi2";
		reg = <0x00 0xff3b0000 0x00 0x10000>;
		reg-names = "csihost_regs";
		interrupts = <0x00 0x1c 0x04 0x00 0x1d 0x04>;
		interrupt-names = "csi-intr1\0csi-intr2";
		clocks = <0x02 0x161>;
		clock-names = "pclk_csi2host";
		resets = <0x02 0xc3>;
		reset-names = "srst_csihost_p";
		status = "disabled";
	};

	csi2-dphy0-hw@ff3c0000 {
		compatible = "rockchip,rk3562-csi2-dphy-hw";
		reg = <0x00 0xff3c0000 0x00 0x10000>;
		clocks = <0x02 0x162>;
		clock-names = "pclk";
		resets = <0x02 0xc4>;
		reset-names = "srst_p_csiphy0";
		rockchip,grf = <0x0d>;
		status = "okay";
		phandle = <0x15>;
	};

	csi2-dphy1-hw@ff3d0000 {
		compatible = "rockchip,rk3562-csi2-dphy-hw";
		reg = <0x00 0xff3d0000 0x00 0x10000>;
		clocks = <0x02 0x163>;
		clock-names = "pclk";
		resets = <0x02 0xc5>;
		reset-names = "srst_p_csiphy1";
		rockchip,grf = <0x0d>;
		status = "disabled";
		phandle = <0x19>;
	};

	rkcif@ff3e0000 {
		compatible = "rockchip,rk3562-cif";
		reg = <0x00 0xff3e0000 0x00 0x800>;
		reg-names = "cif_regs";
		interrupts = <0x00 0x63 0x04>;
		interrupt-names = "cif-intr";
		clocks = <0x02 0x157 0x02 0x158 0x02 0x159 0x02 0x15a 0x02 0x15b 0x02 0x15c 0x02 0x15d>;
		clock-names = "aclk_cif\0hclk_cif\0dclk_cif\0csirx0_data\0csirx1_data\0csirx2_data\0csirx3_data";
		resets = <0x02 0xb9 0x02 0xba 0x02 0xbb 0x02 0xbc 0x02 0xbd 0x02 0xbe 0x02 0xbf>;
		reset-names = "rst_cif_a\0rst_cif_h\0rst_cif_d\0rst_cif_i0\0rst_cif_i1\0rst_cif_i2\0rst_cif_i3";
		power-domains = <0x36 0x0c>;
		rockchip,grf = <0x0d>;
		iommus = <0x28>;
		status = "okay";
		phandle = <0x27>;
	};

	iommu@ff3e0800 {
		compatible = "rockchip,iommu-v2";
		reg = <0x00 0xff3e0800 0x00 0x100>;
		interrupts = <0x00 0x64 0x04>;
		interrupt-names = "cif_mmu";
		clocks = <0x02 0x157 0x02 0x158>;
		clock-names = "aclk\0iface";
		power-domains = <0x36 0x0c>;
		rockchip,disable-mmu-reset;
		#iommu-cells = <0x00>;
		status = "okay";
		phandle = <0x28>;
	};

	isp@ff3f0000 {
		compatible = "rockchip,rk3562-rkisp";
		reg = <0x00 0xff3f0000 0x00 0x7f00>;
		interrupts = <0x00 0x58 0x04 0x00 0x59 0x04 0x00 0x5a 0x04>;
		interrupt-names = "mipi_irq\0mi_irq\0isp_irq";
		clocks = <0x02 0x154 0x02 0x155 0x02 0x156>;
		clock-names = "aclk_isp\0hclk_isp\0clk_isp_core";
		power-domains = <0x36 0x0c>;
		iommus = <0x6b>;
		status = "okay";
		phandle = <0x2f>;
	};

	iommu@ff3f7f00 {
		compatible = "rockchip,iommu-v2";
		reg = <0x00 0xff3f7f00 0x00 0x100>;
		interrupts = <0x00 0x5b 0x04>;
		interrupt-names = "isp_mmu";
		clocks = <0x02 0x154 0x02 0x155>;
		clock-names = "aclk\0iface";
		rockchip,disable-mmu-reset;
		#iommu-cells = <0x00>;
		power-domains = <0x36 0x0c>;
		status = "okay";
		phandle = <0x6b>;
	};

	vop@ff400000 {
		compatible = "rockchip,rk3562-vop";
		reg = <0x00 0xff400000 0x00 0x2000 0x00 0xff405000 0x00 0x1000>;
		reg-names = "regs\0gamma_lut";
		interrupts = <0x00 0x87 0x04>;
		clocks = <0x02 0x166 0x02 0x167 0x02 0x168>;
		clock-names = "aclk_vop\0hclk_vop\0dclk_vp0";
		resets = <0x02 0xd6 0x02 0xd7 0x02 0xd8>;
		reset-names = "axi\0ahb\0dclk_vp0";
		iommus = <0x6c>;
		power-domains = <0x36 0x0d>;
		rockchip,grf = <0x6d>;
		assigned-clocks = <0x02 0x168>;
		assigned-clock-parents = <0x02 0x03>;
		status = "okay";

		ports {
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			phandle = <0x1a>;

			port@0 {
				#address-cells = <0x01>;
				#size-cells = <0x00>;
				reg = <0x00>;

				endpoint@0 {
					reg = <0x00>;
					remote-endpoint = <0x6e>;
					phandle = <0x1f>;
				};

				endpoint@1 {
					reg = <0x01>;
					remote-endpoint = <0x6f>;
					phandle = <0x1d>;
				};

				endpoint@2 {
					reg = <0x02>;
					remote-endpoint = <0x70>;
					phandle = <0x1e>;
				};
			};
		};
	};

	iommu@ff407e00 {
		compatible = "rockchip,iommu-v2";
		reg = <0x00 0xff407e00 0x00 0x100>;
		interrupts = <0x00 0x87 0x04>;
		interrupt-names = "vop_mmu";
		clocks = <0x02 0x166 0x02 0x167>;
		clock-names = "aclk\0iface";
		#iommu-cells = <0x00>;
		rockchip,disable-device-link-resume;
		rockchip,shootdown-entire;
		status = "okay";
		phandle = <0x6c>;
	};

	rga@ff440000 {
		compatible = "rockchip,rga2_core0";
		reg = <0x00 0xff440000 0x00 0x1000>;
		interrupts = <0x00 0x7e 0x04>;
		interrupt-names = "rga2_irq";
		clocks = <0x02 0x142 0x02 0x143 0x02 0x144>;
		clock-names = "aclk_rga2\0hclk_rga2\0clk_rga2";
		iommus = <0x71>;
		power-domains = <0x36 0x0e>;
		status = "okay";
	};

	iommu@ff440f00 {
		compatible = "rockchip,iommu-v2";
		reg = <0x00 0xff440f00 0x00 0x100>;
		interrupts = <0x00 0x7e 0x04>;
		interrupt-names = "rga2_mmu";
		clocks = <0x02 0x142 0x02 0x143>;
		clock-names = "aclk\0iface";
		#iommu-cells = <0x00>;
		power-domains = <0x36 0x0e>;
		status = "okay";
		phandle = <0x71>;
	};

	jpegd@ff450000 {
		compatible = "rockchip,rkv-jpeg-decoder-v1";
		reg = <0x00 0xff450000 0x00 0x400>;
		interrupts = <0x00 0x79 0x04>;
		clocks = <0x02 0x145 0x02 0x146>;
		clock-names = "aclk_vcodec\0hclk_vcodec";
		rockchip,disable-auto-freq;
		resets = <0x02 0xe9 0x02 0xea>;
		reset-names = "video_a\0video_h";
		power-domains = <0x36 0x0e>;
		iommus = <0x72>;
		rockchip,srv = <0x67>;
		rockchip,taskqueue-node = <0x02>;
		rockchip,resetgroup-node = <0x02>;
		status = "okay";
	};

	iommu@ff450480 {
		compatible = "rockchip,iommu-v2";
		reg = <0x00 0xff450480 0x00 0x40>;
		interrupts = <0x00 0x78 0x04>;
		interrupt-names = "jpegd_mmu";
		clock-names = "aclk\0iface";
		clocks = <0x02 0x145 0x02 0x146>;
		power-domains = <0x36 0x0e>;
		rockchip,shootdown-entire;
		#iommu-cells = <0x00>;
		status = "okay";
		phandle = <0x72>;
	};

	dfi@ff4c0000 {
		reg = <0x00 0xff4c0000 0x00 0x400>;
		compatible = "rockchip,rk3562-dfi";
		rockchip,pmugrf = <0x73>;
		status = "disabled";
		phandle = <0x20>;
	};

	pcie@ff500000 {
		compatible = "rockchip,rk3562-pcie\0snps,dw-pcie";
		#address-cells = <0x03>;
		#size-cells = <0x02>;
		bus-range = <0x00 0xff>;
		clocks = <0x02 0x107 0x02 0x108 0x02 0x109 0x02 0x10a 0x02 0x10b>;
		clock-names = "aclk_mst\0aclk_slv\0aclk_dbi\0pclk\0aux";
		device_type = "pci";
		interrupts = <0x00 0x92 0x04 0x00 0x91 0x04 0x00 0x90 0x04 0x00 0x8f 0x04 0x00 0x8e 0x04 0x00 0x8d 0x04>;
		interrupt-names = "msi\0pmc\0sys\0legacy\0msg\0err";
		#interrupt-cells = <0x01>;
		interrupt-map-mask = <0x00 0x00 0x00 0x07>;
		interrupt-map = <0x00 0x00 0x00 0x01 0x74 0x00 0x00 0x00 0x00 0x02 0x74 0x01 0x00 0x00 0x00 0x03 0x74 0x02 0x00 0x00 0x00 0x04 0x74 0x03>;
		linux,pci-domain = <0x00>;
		num-ib-windows = <0x08>;
		num-viewport = <0x08>;
		num-ob-windows = <0x02>;
		max-link-speed = <0x02>;
		num-lanes = <0x01>;
		phys = <0x75 0x02>;
		phy-names = "pcie-phy";
		power-domains = <0x36 0x0f>;
		ranges = <0x800 0x00 0xfc000000 0x00 0xfc000000 0x00 0x100000 0x81000000 0x00 0xfc100000 0x00 0xfc100000 0x00 0x100000 0x82000000 0x00 0xfc200000 0x00 0xfc200000 0x00 0x1e00000 0xc3000000 0x03 0x00 0x03 0x00 0x00 0x40000000>;
		reg = <0x00 0xfe000000 0x00 0x400000 0x00 0xff500000 0x00 0x10000>;
		reg-names = "pcie-dbi\0pcie-apb";
		resets = <0x02 0x108>;
		reset-names = "pipe";
		status = "disabled";

		legacy-interrupt-controller {
			interrupt-controller;
			#address-cells = <0x00>;
			#interrupt-cells = <0x01>;
			interrupt-parent = <0x01>;
			phandle = <0x74>;
		};
	};

	can@ff600000 {
		compatible = "rockchip,rk3562-can";
		reg = <0x00 0xff600000 0x00 0x1000>;
		interrupts = <0x00 0x40 0x04>;
		clocks = <0x02 0xdd 0x02 0xdc>;
		clock-names = "baudclk\0apb_pclk";
		resets = <0x02 0xc00b1 0x02 0xc00b0>;
		reset-names = "can\0can-apb";
		status = "disabled";
	};

	can@ff610000 {
		compatible = "rockchip,rk3562-can";
		reg = <0x00 0xff610000 0x00 0x1000>;
		interrupts = <0x00 0x41 0x04>;
		clocks = <0x02 0xdf 0x02 0xde>;
		clock-names = "baudclk\0apb_pclk";
		resets = <0x02 0xc00b3 0x02 0xc00b2>;
		reset-names = "can\0can-apb";
		status = "disabled";
	};

	spi@ff640000 {
		compatible = "rockchip,rk3066-spi";
		reg = <0x00 0xff640000 0x00 0x1000>;
		interrupts = <0x00 0x35 0x04>;
		#address-cells = <0x01>;
		#size-cells = <0x00>;
		clocks = <0x02 0xa1 0x02 0xa0 0x02 0xa2>;
		clock-names = "spiclk\0apb_pclk\0sclk_in";
		dmas = <0x47 0x0f 0x47 0x0e>;
		dma-names = "tx\0rx";
		pinctrl-names = "default";
		pinctrl-0 = <0x76 0x77 0x78>;
		num-cs = <0x02>;
		status = "disabled";
	};

	spi@ff650000 {
		compatible = "rockchip,rk3066-spi";
		reg = <0x00 0xff650000 0x00 0x1000>;
		interrupts = <0x00 0x36 0x04>;
		#address-cells = <0x01>;
		#size-cells = <0x00>;
		clocks = <0x02 0xa4 0x02 0xa3 0x02 0xa5>;
		clock-names = "spiclk\0apb_pclk\0sclk_in";
		dmas = <0x47 0x11 0x47 0x10>;
		dma-names = "tx\0rx";
		pinctrl-names = "default";
		pinctrl-0 = <0x79 0x7a 0x7b>;
		num-cs = <0x02>;
		status = "disabled";
	};

	serial@ff670000 {
		compatible = "rockchip,rk3562-uart\0snps,dw-apb-uart";
		reg = <0x00 0xff670000 0x00 0x100>;
		interrupts = <0x00 0x1f 0x04>;
		clocks = <0x02 0xb2 0x02 0xa6>;
		clock-names = "baudclk\0apb_pclk";
		reg-shift = <0x02>;
		reg-io-width = <0x04>;
		dmas = <0x47 0x0a 0x47 0x01>;
		status = "disabled";
	};

	serial@ff680000 {
		compatible = "rockchip,rk3562-uart\0snps,dw-apb-uart";
		reg = <0x00 0xff680000 0x00 0x100>;
		interrupts = <0x00 0x20 0x04>;
		clocks = <0x02 0xb6 0x02 0xa7>;
		clock-names = "baudclk\0apb_pclk";
		reg-shift = <0x02>;
		reg-io-width = <0x04>;
		dmas = <0x47 0x02>;
		status = "disabled";
	};

	serial@ff690000 {
		compatible = "rockchip,rk3562-uart\0snps,dw-apb-uart";
		reg = <0x00 0xff690000 0x00 0x100>;
		interrupts = <0x00 0x21 0x04>;
		clocks = <0x02 0xba 0x02 0xa8>;
		clock-names = "baudclk\0apb_pclk";
		reg-shift = <0x02>;
		reg-io-width = <0x04>;
		dmas = <0x47 0x03>;
		status = "disabled";
	};

	serial@ff6a0000 {
		compatible = "rockchip,rk3562-uart\0snps,dw-apb-uart";
		reg = <0x00 0xff6a0000 0x00 0x100>;
		interrupts = <0x00 0x22 0x04>;
		clocks = <0x02 0xbe 0x02 0xa9>;
		clock-names = "baudclk\0apb_pclk";
		reg-shift = <0x02>;
		reg-io-width = <0x04>;
		dmas = <0x47 0x04>;
		status = "disabled";
	};

	serial@ff6b0000 {
		compatible = "rockchip,rk3562-uart\0snps,dw-apb-uart";
		reg = <0x00 0xff6b0000 0x00 0x100>;
		interrupts = <0x00 0x23 0x04>;
		clocks = <0x02 0xc2 0x02 0xaa>;
		clock-names = "baudclk\0apb_pclk";
		reg-shift = <0x02>;
		reg-io-width = <0x04>;
		dmas = <0x47 0x0b 0x47 0x05>;
		status = "disabled";
	};

	serial@ff6c0000 {
		compatible = "rockchip,rk3562-uart\0snps,dw-apb-uart";
		reg = <0x00 0xff6c0000 0x00 0x100>;
		interrupts = <0x00 0x24 0x04>;
		clocks = <0x02 0xc6 0x02 0xab>;
		clock-names = "baudclk\0apb_pclk";
		reg-shift = <0x02>;
		reg-io-width = <0x04>;
		dmas = <0x47 0x06>;
		status = "disabled";
	};

	serial@ff6d0000 {
		compatible = "rockchip,rk3562-uart\0snps,dw-apb-uart";
		reg = <0x00 0xff6d0000 0x00 0x100>;
		interrupts = <0x00 0x25 0x04>;
		clocks = <0x02 0xca 0x02 0xac>;
		clock-names = "baudclk\0apb_pclk";
		reg-shift = <0x02>;
		reg-io-width = <0x04>;
		dmas = <0x47 0x07>;
		status = "disabled";
	};

	serial@ff6e0000 {
		compatible = "rockchip,rk3562-uart\0snps,dw-apb-uart";
		reg = <0x00 0xff6e0000 0x00 0x100>;
		interrupts = <0x00 0x26 0x04>;
		clocks = <0x02 0xce 0x02 0xad>;
		clock-names = "baudclk\0apb_pclk";
		reg-shift = <0x02>;
		reg-io-width = <0x04>;
		dmas = <0x47 0x08>;
		status = "disabled";
	};

	serial@ff6f0000 {
		compatible = "rockchip,rk3562-uart\0snps,dw-apb-uart";
		reg = <0x00 0xff6f0000 0x00 0x100>;
		interrupts = <0x00 0x27 0x04>;
		clocks = <0x02 0xd2 0x02 0xae>;
		clock-names = "baudclk\0apb_pclk";
		reg-shift = <0x02>;
		reg-io-width = <0x04>;
		dmas = <0x47 0x09>;
		status = "okay";
		pinctrl-0 = <0x7c>;
	};

	pwm@ff700000 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff700000 0x00 0x10>;
		interrupts = <0x00 0x16 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x7d>;
		clocks = <0x02 0xd4 0x02 0xd3>;
		clock-names = "pwm\0pclk";
		status = "disabled";
	};

	pwm@ff700010 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff700010 0x00 0x10>;
		interrupts = <0x00 0x16 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x7e>;
		clocks = <0x02 0xd4 0x02 0xd3>;
		clock-names = "pwm\0pclk";
		status = "okay";
		phandle = <0xd1>;
	};

	pwm@ff700020 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff700020 0x00 0x10>;
		interrupts = <0x00 0x16 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x7f>;
		clocks = <0x02 0xd4 0x02 0xd3>;
		clock-names = "pwm\0pclk";
		status = "okay";
		phandle = <0xce>;
	};

	pwm@ff700030 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff700030 0x00 0x10>;
		interrupts = <0x00 0x16 0x04 0x00 0x17 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x80>;
		clocks = <0x02 0xd4 0x02 0xd3>;
		clock-names = "pwm\0pclk";
		status = "disabled";
	};

	pwm@ff710000 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff710000 0x00 0x10>;
		interrupts = <0x00 0x18 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x81>;
		clocks = <0x02 0xd7 0x02 0xd6>;
		clock-names = "pwm\0pclk";
		status = "disabled";
	};

	pwm@ff710010 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff710010 0x00 0x10>;
		interrupts = <0x00 0x18 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x82>;
		clocks = <0x02 0xd7 0x02 0xd6>;
		clock-names = "pwm\0pclk";
		status = "disabled";
	};

	pwm@ff710020 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff710020 0x00 0x10>;
		interrupts = <0x00 0x18 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x83>;
		clocks = <0x02 0xd7 0x02 0xd6>;
		clock-names = "pwm\0pclk";
		status = "disabled";
	};

	pwm@ff710030 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff710030 0x00 0x10>;
		interrupts = <0x00 0x18 0x04 0x00 0x19 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x84>;
		clocks = <0x02 0xd7 0x02 0xd6>;
		clock-names = "pwm\0pclk";
		status = "disabled";
	};

	pwm@ff720000 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff720000 0x00 0x10>;
		interrupts = <0x00 0x1a 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x85>;
		clocks = <0x02 0xda 0x02 0xd9>;
		clock-names = "pwm\0pclk";
		status = "disabled";
	};

	pwm@ff720010 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff720010 0x00 0x10>;
		interrupts = <0x00 0x1a 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x86>;
		clocks = <0x02 0xda 0x02 0xd9>;
		clock-names = "pwm\0pclk";
		status = "okay";
	};

	pwm@ff720020 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff720020 0x00 0x10>;
		interrupts = <0x00 0x1a 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x87>;
		clocks = <0x02 0xda 0x02 0xd9>;
		clock-names = "pwm\0pclk";
		status = "disabled";
	};

	pwm@ff720030 {
		compatible = "rockchip,rk3562-pwm\0rockchip,rk3328-pwm";
		reg = <0x00 0xff720030 0x00 0x10>;
		interrupts = <0x00 0x1a 0x04 0x00 0x1b 0x04>;
		#pwm-cells = <0x03>;
		pinctrl-names = "active";
		pinctrl-0 = <0x88>;
		clocks = <0x02 0xda 0x02 0xd9>;
		clock-names = "pwm\0pclk";
		status = "disabled";
	};

	saradc@ff730000 {
		compatible = "rockchip,rk3562-saradc";
		reg = <0x00 0xff730000 0x00 0x100>;
		interrupts = <0x00 0x28 0x04>;
		#io-channel-cells = <0x01>;
		clocks = <0x02 0xfe 0x02 0xfd>;
		clock-names = "saradc\0apb_pclk";
		resets = <0x02 0xc0104>;
		reset-names = "saradc-apb";
		status = "okay";
		vref-supply = <0x89>;
		phandle = <0xd6>;
	};

	usb2-phy@ff740000 {
		compatible = "rockchip,rk3562-usb2phy";
		reg = <0x00 0xff740000 0x00 0x10000>;
		clocks = <0x02 0x13d 0x02 0xfb>;
		clock-names = "phyclk\0pclk";
		#clock-cells = <0x00>;
		clock-output-names = "usb480m_phy";
		rockchip,usbgrf = <0x8a>;
		status = "okay";
		phandle = <0x37>;

		otg-port {
			#phy-cells = <0x00>;
			interrupts = <0x00 0x99 0x04 0x00 0x9a 0x04 0x00 0x9b 0x04>;
			interrupt-names = "otg-bvalid\0otg-id\0linestate";
			status = "okay";
			vbus-supply = <0x8b>;
			phandle = <0x35>;
		};

		host-port {
			#phy-cells = <0x00>;
			interrupts = <0x00 0x9c 0x04>;
			interrupt-names = "linestate";
			status = "okay";
			phy-supply = <0x8b>;
			phandle = <0x38>;
		};
	};

	phy@ff750000 {
		compatible = "rockchip,rk3562-naneng-combphy";
		reg = <0x00 0xff750000 0x00 0x100>;
		#phy-cells = <0x01>;
		clocks = <0x02 0x13a 0x02 0xfc 0x02 0x106>;
		clock-names = "refclk\0apbclk\0pipe_clk";
		assigned-clocks = <0x02 0x13a>;
		assigned-clock-rates = <0x5f5e100>;
		resets = <0x02 0xc00f7 0x02 0x113>;
		reset-names = "combphy-apb\0combphy";
		rockchip,pipe-grf = <0x8c>;
		rockchip,pipe-phy-grf = <0x8d>;
		status = "okay";
		phandle = <0x75>;
	};

	sai@ff800000 {
		compatible = "rockchip,rk3562-sai\0rockchip,sai-v1";
		reg = <0x00 0xff800000 0x00 0x1000>;
		interrupts = <0x00 0x4e 0x04>;
		clocks = <0x02 0x78 0x02 0x74>;
		clock-names = "mclk\0hclk";
		dmas = <0x47 0x13 0x47 0x12>;
		dma-names = "tx\0rx";
		resets = <0x02 0xc0023 0x02 0xc0020>;
		reset-names = <0x6d006800>;
		pinctrl-names = "default";
		pinctrl-0 = <0x8e 0x8f 0x90 0x91>;
		#sound-dai-cells = <0x00>;
		status = "okay";
		phandle = <0xd7>;
	};

	sai@ff810000 {
		compatible = "rockchip,rk3562-sai\0rockchip,sai-v1";
		reg = <0x00 0xff810000 0x00 0x1000>;
		interrupts = <0x00 0x4f 0x04>;
		clocks = <0x02 0x7e 0x02 0x7a>;
		clock-names = "mclk\0hclk";
		dmas = <0x47 0x15 0x47 0x14>;
		dma-names = "tx\0rx";
		resets = <0x02 0xc0028 0x02 0xc0025>;
		reset-names = <0x6d006800>;
		pinctrl-names = "default";
		pinctrl-0 = <0x92 0x93 0x94 0x95 0x96 0x97 0x98 0x99 0x9a 0x9b>;
		#sound-dai-cells = <0x00>;
		status = "disabled";
	};

	sai@ff820000 {
		compatible = "rockchip,rk3562-sai\0rockchip,sai-v1";
		reg = <0x00 0xff820000 0x00 0x1000>;
		interrupts = <0x00 0x50 0x04>;
		clocks = <0x02 0x84 0x02 0x80>;
		clock-names = "mclk\0hclk";
		dmas = <0x47 0x17 0x47 0x16>;
		dma-names = "tx\0rx";
		resets = <0x02 0xc002d 0x02 0xc002a>;
		reset-names = <0x6d006800>;
		pinctrl-names = "default";
		pinctrl-0 = <0x9c 0x9d 0x9e 0x9f>;
		#sound-dai-cells = <0x00>;
		status = "disabled";
	};

	pdm@ff830000 {
		compatible = "rockchip,rk3562-pdm\0rockchip,rv1126-pdm";
		reg = <0x00 0xff830000 0x00 0x1000>;
		clocks = <0x02 0x89 0x02 0x88>;
		clock-names = "pdm_clk\0pdm_hclk";
		dmas = <0x47 0x1f>;
		dma-names = "rx";
		pinctrl-names = "default";
		pinctrl-0 = <0xa0 0xa1 0xa2 0xa3 0xa4 0xa5>;
		#sound-dai-cells = <0x00>;
		status = "disabled";
	};

	spdif@ff840000 {
		compatible = "rockchip,rk3562-spdif\0rockchip,rk3568-spdif";
		reg = <0x00 0xff840000 0x00 0x1000>;
		interrupts = <0x00 0x7f 0x04>;
		dmas = <0x47 0x1e>;
		dma-names = "tx";
		clock-names = "mclk\0hclk";
		clocks = <0x02 0x8e 0x02 0x8a>;
		#sound-dai-cells = <0x00>;
		pinctrl-names = "default";
		pinctrl-0 = <0xa6>;
		status = "okay";
	};

	dsm@ff850000 {
		compatible = "rockchip,rk3562-dsm";
		reg = <0x00 0xff850000 0x00 0x1000>;
		clocks = <0x02 0x87 0x02 0x86>;
		clock-names = "dac\0pclk";
		resets = <0x02 0xc0032>;
		reset-names = "reset";
		rockchip,grf = <0x8c>;
		pinctrl-names = "default";
		pinctrl-0 = <0xa7>;
		#sound-dai-cells = <0x00>;
		status = "disabled";
	};

	spi@ff860000 {
		compatible = "rockchip,sfc";
		reg = <0x00 0xff860000 0x00 0x10000>;
		interrupts = <0x00 0x80 0x04>;
		clocks = <0x02 0x9c 0x02 0x9d>;
		clock-names = "clk_sfc\0hclk_sfc";
		assigned-clocks = <0x02 0x9c>;
		assigned-clock-rates = <0x5f5e100>;
		#address-cells = <0x01>;
		#size-cells = <0x00>;
		status = "disabled";
	};

	sdhci@ff870000 {
		compatible = "rockchip,rk3562-dwcmshc\0rockchip,dwcmshc-sdhci";
		reg = <0x00 0xff870000 0x00 0x10000>;
		interrupts = <0x00 0x3f 0x04>;
		assigned-clocks = <0x02 0x9a 0x02 0x99>;
		assigned-clock-rates = <0xbebc200 0xbebc200>;
		clocks = <0x02 0x99 0x02 0x97 0x02 0x98 0x02 0x9a 0x02 0x9b>;
		clock-names = "core\0bus\0axi\0block\0timer";
		resets = <0x02 0xc004a 0x02 0xc0048 0x02 0xc0049 0x02 0xc004b 0x02 0xc004c>;
		reset-names = "core\0bus\0axi\0block\0timer";
		max-frequency = <0xbebc200>;
		status = "okay";
		bus-width = <0x08>;
		no-sdio;
		no-sd;
		non-removable;
		mmc-hs400-1_8v;
		mmc-hs400-enhanced-strobe;
		full-pwr-cycle-in-suspend;
	};

	mmc@ff880000 {
		compatible = "rockchip,rk3562-dw-mshc\0rockchip,rk3288-dw-mshc";
		reg = <0x00 0xff880000 0x00 0x10000>;
		interrupts = <0x00 0x38 0x04>;
		max-frequency = <0xbebc200>;
		clocks = <0x02 0x8f 0x02 0x90 0x02 0x93 0x02 0x94>;
		clock-names = "biu\0ciu\0ciu-drive\0ciu-sample";
		resets = <0x02 0xc0040>;
		reset-names = "reset";
		fifo-depth = <0x100>;
		status = "okay";
		no-sdio;
		no-mmc;
		bus-width = <0x04>;
		cap-mmc-highspeed;
		cap-sd-highspeed;
		disable-wp;
		sd-uhs-sdr104;
		vmmc-supply = <0xa8>;
		vqmmc-supply = <0xa9>;
		pinctrl-names = "default";
		pinctrl-0 = <0xaa 0xab 0xac 0xad>;
	};

	mmc@ff890000 {
		compatible = "rockchip,rk3562-dw-mshc\0rockchip,rk3288-dw-mshc";
		reg = <0x00 0xff890000 0x00 0x10000>;
		interrupts = <0x00 0x39 0x04>;
		max-frequency = <0xbebc200>;
		clocks = <0x02 0x91 0x02 0x92 0x02 0x95 0x02 0x96>;
		clock-names = "biu\0ciu\0ciu-drive\0ciu-sample";
		resets = <0x02 0xc0042>;
		reset-names = "reset";
		fifo-depth = <0x100>;
		status = "disabled";
	};

	crypto@ff8a0000 {
		compatible = "rockchip,crypto-v4";
		reg = <0x00 0xff8a0000 0x00 0x2000>;
		interrupts = <0x00 0x74 0x04>;
		clocks = <0x05 0xe0 0x05 0xe1 0x05 0xe3 0x05 0xe4 0x05 0xe2>;
		clock-names = "aclk\0hclk\0sclk\0pka\0pclk";
		assigned-clocks = <0x05 0xe3 0x05 0xe4>;
		assigned-clock-rates = <0xbebc200 0x11e1a300>;
		resets = <0x02 0xc00c3>;
		reset-names = "crypto-rst";
		status = "disabled";
	};

	rng@ff8e0000 {
		compatible = "rockchip,rkrng";
		reg = <0x00 0xff8e0000 0x00 0x200>;
		interrupts = <0x00 0x5d 0x04>;
		clocks = <0x05 0xe7>;
		clock-names = "hclk_trng";
		resets = <0x02 0xc00c7>;
		reset-names = "reset";
		status = "okay";
	};

	otp@ff930000 {
		compatible = "rockchip,rk3562-otp";
		reg = <0x00 0xff930000 0x00 0x4000>;
		#address-cells = <0x01>;
		#size-cells = <0x01>;
		clocks = <0x02 0xf5 0x02 0xf4 0x02 0xf3 0x02 0xf9 0x02 0xfa>;
		clock-names = "usr\0sbpi\0apb\0arb\0phy";
		resets = <0x02 0xc00e2 0x02 0xc00e1 0x02 0xc00e0 0x02 0xc00e6 0x02 0xc00e7>;
		reset-names = "usr\0sbpi\0apb\0arb\0phy";

		cpu-code@2 {
			reg = <0x02 0x02>;
			phandle = <0x14>;
		};

		cpu-version@8 {
			reg = <0x08 0x01>;
			bits = <0x03 0x03>;
			phandle = <0x13>;
		};

		mbist-vmin@9 {
			reg = <0x09 0x01>;
			bits = <0x00 0x02>;
			phandle = <0x0b>;
		};

		log-mbist-vmin@9 {
			reg = <0x09 0x01>;
			bits = <0x04 0x02>;
			phandle = <0x25>;
		};

		id@a {
			reg = <0x0a 0x10>;
			phandle = <0x12>;
		};

		cpu-leakage@1a {
			reg = <0x1a 0x01>;
			phandle = <0x09>;
		};

		log-leakage@1b {
			reg = <0x1b 0x01>;
			phandle = <0x23>;
		};

		npu-leakage@1c {
			reg = <0x1c 0x01>;
			phandle = <0x5e>;
		};

		gpu-leakage@1d {
			reg = <0x1d 0x01>;
			phandle = <0x63>;
		};

		cpu-opp-info@2e {
			reg = <0x2e 0x06>;
			phandle = <0x0a>;
		};

		gpu-opp-info@34 {
			reg = <0x34 0x06>;
			phandle = <0x64>;
		};

		npu-opp-info@3a {
			reg = <0x3a 0x06>;
			phandle = <0x5f>;
		};

		dmc-opp-info@40 {
			reg = <0x40 0x06>;
			phandle = <0x24>;
		};

		cpu-pvtpll@46 {
			reg = <0x46 0x02>;
			phandle = <0x0c>;
		};

		gpu-pvtpll@48 {
			reg = <0x48 0x02>;
			phandle = <0x65>;
		};

		npu-pvtpll@4a {
			reg = <0x4a 0x02>;
			phandle = <0x60>;
		};
	};

	dma-controller@ff990000 {
		compatible = "arm,pl330\0arm,primecell";
		reg = <0x00 0xff990000 0x00 0x4000>;
		interrupts = <0x00 0x6f 0x04 0x00 0x6e 0x04>;
		clocks = <0x02 0xf1>;
		clock-names = "apb_pclk";
		#dma-cells = <0x01>;
		arm,pl330-periph-burst;
		phandle = <0x47>;
	};

	dma-controller@ff9a0000 {
		compatible = "rockchip,rk3562-dma\0rockchip,dma-v1";
		reg = <0x00 0xff9a0000 0x00 0x4000>;
		interrupts = <0x00 0x70 0x04>;
		clocks = <0x02 0xf2>;
		clock-names = "aclk";
		#dma-cells = <0x01>;
		dma-channels = <0x2a>;
		dma-requests = <0x2a>;
		rockchip,grf = <0x8c>;
	};

	hwspinlock@ff9e0000 {
		compatible = "rockchip,hwspinlock";
		reg = <0x00 0xff9e0000 0x00 0x100>;
		#hwlock-cells = <0x01>;
		status = "disabled";
	};

	i2c@ffa00000 {
		compatible = "rockchip,rk3562-i2c\0rockchip,rk3399-i2c";
		reg = <0x00 0xffa00000 0x00 0x1000>;
		clocks = <0x02 0x20 0x02 0x1a>;
		clock-names = "i2c\0pclk";
		interrupts = <0x00 0x0d 0x04>;
		pinctrl-names = "default";
		pinctrl-0 = <0xae>;
		#address-cells = <0x01>;
		#size-cells = <0x00>;
		status = "disabled";
	};

	i2c@ffa10000 {
		compatible = "rockchip,rk3562-i2c\0rockchip,rk3399-i2c";
		reg = <0x00 0xffa10000 0x00 0x1000>;
		clocks = <0x02 0x21 0x02 0x1b>;
		clock-names = "i2c\0pclk";
		interrupts = <0x00 0x0e 0x04>;
		pinctrl-names = "default";
		pinctrl-0 = <0xaf>;
		#address-cells = <0x01>;
		#size-cells = <0x00>;
		status = "okay";

		ft5x06@38 {
			status = "okay";
			compatible = "edt,edt-ft5306";
			reg = <0x38>;
			touch-gpio = <0x3d 0x13 0x01>;
			interrupt-parent = <0x3d>;
			interrupts = <0x13 0x08>;
			reset-gpios = <0x3d 0x0f 0x01>;
			touchscreen-size-x = <0x320>;
			touchscreen-size-y = <0x500>;
			touch_type = <0x01>;
		};
	};

	i2c@ffa20000 {
		compatible = "rockchip,rk3562-i2c\0rockchip,rk3399-i2c";
		reg = <0x00 0xffa20000 0x00 0x1000>;
		clocks = <0x02 0x22 0x02 0x1c>;
		clock-names = "i2c\0pclk";
		interrupts = <0x00 0x0f 0x04>;
		pinctrl-names = "default";
		pinctrl-0 = <0xb0>;
		#address-cells = <0x01>;
		#size-cells = <0x00>;
		status = "okay";

		hym8563@51 {
			compatible = "haoyu,hym8563";
			reg = <0x51>;
			#clock-cells = <0x00>;
			clock-frequency = <0x8000>;
			clock-output-names = "hym8563";
			pinctrl-names = "default";
			pinctrl-0 = <0xb1>;
			interrupt-parent = <0x3d>;
			interrupts = <0x05 0x08>;
			wakeup-source;
			status = "okay";
		};
	};

	i2c@ffa30000 {
		compatible = "rockchip,rk3562-i2c\0rockchip,rk3399-i2c";
		reg = <0x00 0xffa30000 0x00 0x1000>;
		clocks = <0x02 0x23 0x02 0x1d>;
		clock-names = "i2c\0pclk";
		interrupts = <0x00 0x10 0x04>;
		pinctrl-names = "default";
		pinctrl-0 = <0xb2>;
		#address-cells = <0x01>;
		#size-cells = <0x00>;
		status = "okay";

		ov5695@36 {
			status = "okay";
			compatible = "ovti,ov5695";
			reg = <0x36>;
			clocks = <0x02 0x13>;
			clock-names = "xvclk";
			power-domains = <0x36 0x0c>;
			pinctrl-names = "default";
			pinctrl-0 = <0xb3>;
			reset-gpios = <0xb4 0x0c 0x01>;
			pwdn-gpios = <0xb4 0x10 0x00>;
			rockchip,camera-module-index = <0x00>;
			rockchip,camera-module-facing = "front";
			rockchip,camera-module-name = "default";
			rockchip,camera-module-lens-name = "default";

			port {

				endpoint {
					remote-endpoint = <0xb5>;
					data-lanes = <0x01 0x02>;
					phandle = <0x16>;
				};
			};
		};

		ov13850@10 {
			status = "okay";
			compatible = "otvi,ov13850";
			reg = <0x10>;
			clocks = <0x02 0x13>;
			clock-names = "xvclk";
			power-domains = <0x36 0x0c>;
			pinctrl-names = "default";
			pinctrl-0 = <0xb3>;
			reset-gpios = <0xb4 0x0c 0x00>;
			pwdn-gpios = <0xb4 0x10 0x00>;
			rockchip,camera-module-index = <0x01>;
			rockchip,camera-module-facing = "back";
			rockchip,camera-module-name = "default";
			rockchip,camera-module-lens-name = "default";

			port {

				endpoint {
					remote-endpoint = <0xb6>;
					data-lanes = <0x01 0x02 0x03 0x04>;
					phandle = <0x17>;
				};
			};
		};
	};

	i2c@ffa40000 {
		compatible = "rockchip,rk3562-i2c\0rockchip,rk3399-i2c";
		reg = <0x00 0xffa40000 0x00 0x1000>;
		clocks = <0x02 0x24 0x02 0x1e>;
		clock-names = "i2c\0pclk";
		interrupts = <0x00 0x11 0x04>;
		pinctrl-names = "default";
		pinctrl-0 = <0xb7>;
		#address-cells = <0x01>;
		#size-cells = <0x00>;
		status = "disabled";
	};

	timer@ffa50000 {
		compatible = "rockchip,rk3562-timer\0rockchip,rk3288-timer";
		reg = <0x00 0xffa50000 0x00 0x20>;
		interrupts = <0x00 0x2d 0x04>;
		clocks = <0x02 0x28 0x02 0x29>;
		clock-names = "pclk\0timer";
	};

	watchdog@ffa60000 {
		compatible = "snps,dw-wdt";
		reg = <0x00 0xffa60000 0x00 0x100>;
		clocks = <0x02 0x33 0x02 0x32>;
		clock-names = "tclk\0pclk";
		interrupts = <0x00 0x66 0x04>;
		status = "okay";
	};

	tsadc@ffa70000 {
		compatible = "rockchip,rk3562-tsadc";
		reg = <0x00 0xffa70000 0x00 0x400>;
		rockchip,grf = <0x0d>;
		interrupts = <0x00 0x7d 0x04>;
		clocks = <0x02 0x41 0x02 0x42 0x02 0x40>;
		clock-names = "tsadc\0tsadc_tsen\0apb_pclk";
		assigned-clocks = <0x02 0x41 0x02 0x42>;
		assigned-clock-rates = <0x124f80 0xb71b00>;
		resets = <0x02 0x181 0x02 0x180 0x02 0x182>;
		reset-names = "tsadc\0tsadc-apb\0tsadc-phy";
		#thermal-sensor-cells = <0x01>;
		rockchip,hw-tshut-temp = <0x1d4c0>;
		rockchip,hw-tshut-mode = <0x00>;
		rockchip,hw-tshut-polarity = <0x00>;
		status = "okay";
		phandle = <0x31>;
	};

	ethernet@ffa80000 {
		compatible = "rockchip,rk3562-gmac\0snps,dwmac-4.20a";
		reg = <0x00 0xffa80000 0x00 0x10000>;
		interrupts = <0x00 0x49 0x04 0x00 0x46 0x04>;
		interrupt-names = "macirq\0eth_wake_irq";
		rockchip,grf = <0x0d>;
		rockchip,php_grf = <0x6d>;
		clocks = <0x02 0x47 0x02 0x48 0x02 0x45 0x02 0x46>;
		clock-names = "stmmaceth\0clk_mac_ref\0pclk_mac\0aclk_mac";
		resets = <0x02 0x191>;
		reset-names = "stmmaceth";
		snps,mixed-burst;
		snps,tso;
		snps,axi-config = <0xb8>;
		snps,mtl-rx-config = <0xb9>;
		snps,mtl-tx-config = <0xba>;
		status = "okay";
		phy-mode = "rgmii-rxid";
		clock_in_out = "output";
		snps,reset-gpio = <0x3d 0x16 0x01>;
		snps,reset-active-low;
		snps,reset-delays-us = <0x00 0x4e20 0x186a0>;
		tx_delay = <0x44>;
		pinctrl-names = "default";
		pinctrl-0 = <0xbb 0xbc 0xbd 0xbe 0xbf 0xc0 0xc1>;
		phy-handle = <0xc2>;

		mdio {
			compatible = "snps,dwmac-mdio";
			#address-cells = <0x01>;
			#size-cells = <0x00>;
			status = "okay";

			phy@0 {
				compatible = "ethernet-phy-ieee802.3-c22";
				reg = <0x00>;
				phandle = <0xc2>;
			};
		};

		stmmac-axi-config {
			snps,wr_osr_lmt = <0x04>;
			snps,rd_osr_lmt = <0x08>;
			snps,blen = <0x00 0x00 0x00 0x00 0x10 0x08 0x04>;
			phandle = <0xb8>;
		};

		rx-queues-config {
			snps,rx-queues-to-use = <0x01>;
			phandle = <0xb9>;

			queue0 {
			};
		};

		tx-queues-config {
			snps,tx-queues-to-use = <0x01>;
			phandle = <0xba>;

			queue0 {
			};
		};
	};

	saradc@ffaa0000 {
		compatible = "rockchip,rk3562-saradc";
		reg = <0x00 0xffaa0000 0x00 0x100>;
		interrupts = <0x00 0x7c 0x04>;
		#io-channel-cells = <0x01>;
		clocks = <0x02 0x44 0x02 0x56>;
		clock-names = "saradc\0apb_pclk";
		resets = <0x02 0x1a4>;
		reset-names = "saradc-apb";
		status = "disabled";
	};

	mailbox@ffae0000 {
		compatible = "rockchip,rk3562-mailbox\0rockchip,rk3368-mailbox";
		reg = <0x00 0xffae0000 0x00 0x200>;
		interrupts = <0x00 0x72 0x04>;
		clocks = <0x02 0x36>;
		clock-names = "pclk_mailbox";
		#mbox-cells = <0x01>;
		status = "disabled";
	};

	dsi@ffb10000 {
		compatible = "rockchip,rk3562-mipi-dsi";
		reg = <0x00 0xffb10000 0x00 0x10000>;
		interrupts = <0x00 0x84 0x04>;
		clocks = <0x02 0x4e>;
		clock-names = "pclk";
		resets = <0x02 0x199>;
		reset-names = "apb";
		phys = <0x39>;
		phy-names = "dphy";
		rockchip,grf = <0x0d>;
		#address-cells = <0x01>;
		#size-cells = <0x00>;
		status = "disabled";

		ports {
			#address-cells = <0x01>;
			#size-cells = <0x00>;

			port@0 {
				reg = <0x00>;
				#address-cells = <0x01>;
				#size-cells = <0x00>;

				endpoint@0 {
					reg = <0x00>;
					remote-endpoint = <0x1d>;
					status = "disabled";
					phandle = <0x6f>;
				};
			};
		};
	};

	phy@ffb20000 {
		compatible = "rockchip,rk3562-dsi-dphy\0rockchip,rk3562-video-phy\0rockchip,rk3568-dsi-dphy\0rockchip,rk3568-video-phy";
		reg = <0x00 0xffb20000 0x00 0x10000 0x00 0xffb10000 0x00 0x10000>;
		reg-names = "phy\0host";
		clocks = <0x02 0x13f 0x02 0x4d 0x02 0x4e>;
		clock-names = "ref\0pclk\0pclk_host";
		#clock-cells = <0x00>;
		resets = <0x02 0x198>;
		reset-names = "apb";
		#phy-cells = <0x00>;
		status = "okay";
		phandle = <0x39>;
	};

	ethernet@ffb30000 {
		compatible = "rockchip,rk3562-gmac";
		reg = <0x00 0xffb30000 0x00 0x10000>;
		interrupts = <0x00 0x43 0x04 0x00 0x44 0x04>;
		interrupt-names = "macirq\0eth_wake_irq";
		rockchip,grf = <0x0d>;
		rockchip,php_grf = <0x6d>;
		clocks = <0x02 0x5a 0x02 0x5a 0x02 0x57 0x02 0x59>;
		clock-names = "stmmaceth\0clk_mac_ref\0pclk_mac\0aclk_mac";
		resets = <0x02 0x1b1>;
		reset-names = "stmmaceth";
		status = "disabled";

		mdio {
			compatible = "snps,dwmac-mdio";
			#address-cells = <0x01>;
			#size-cells = <0x00>;
		};
	};

	pinctrl {
		compatible = "rockchip,rk3562-pinctrl";
		rockchip,grf = <0x6d>;
		#address-cells = <0x02>;
		#size-cells = <0x02>;
		ranges;
		phandle = <0xc3>;

		gpio@ff260000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x00 0xff260000 0x00 0x100>;
			interrupts = <0x00 0x00 0x04>;
			clocks = <0x02 0x11d 0x02 0x11e>;
			gpio-controller;
			#gpio-cells = <0x02>;
			gpio-ranges = <0xc3 0x00 0x00 0x20>;
			interrupt-controller;
			#interrupt-cells = <0x02>;
			phandle = <0x3d>;
		};

		gpio@ff620000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x00 0xff620000 0x00 0x100>;
			interrupts = <0x00 0x02 0x04>;
			clocks = <0x02 0x100 0x02 0x103>;
			gpio-controller;
			#gpio-cells = <0x02>;
			gpio-ranges = <0xc3 0x00 0x20 0x20>;
			interrupt-controller;
			#interrupt-cells = <0x02>;
		};

		gpio@ff630000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x00 0xff630000 0x00 0x100>;
			interrupts = <0x00 0x04 0x04>;
			clocks = <0x02 0x101 0x02 0x104>;
			gpio-controller;
			#gpio-cells = <0x02>;
			gpio-ranges = <0xc3 0x00 0x40 0x20>;
			interrupt-controller;
			#interrupt-cells = <0x02>;
		};

		gpio@ffac0000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x00 0xffac0000 0x00 0x100>;
			interrupts = <0x00 0x06 0x04>;
			clocks = <0x02 0x54 0x02 0x26>;
			gpio-controller;
			#gpio-cells = <0x02>;
			gpio-ranges = <0xc3 0x00 0x60 0x20>;
			interrupt-controller;
			#interrupt-cells = <0x02>;
			phandle = <0xb4>;
		};

		gpio@ffad0000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x00 0xffad0000 0x00 0x100>;
			interrupts = <0x00 0x08 0x04>;
			clocks = <0x02 0x55 0x02 0x27>;
			gpio-controller;
			#gpio-cells = <0x02>;
			gpio-ranges = <0xc3 0x00 0x80 0x20>;
			interrupt-controller;
			#interrupt-cells = <0x02>;
			phandle = <0xda>;
		};

		pcfg-pull-up {
			bias-pull-up;
			phandle = <0xc7>;
		};

		pcfg-pull-none {
			bias-disable;
			phandle = <0xc4>;
		};

		pcfg-pull-none-drv-level-1 {
			bias-disable;
			drive-strength = <0x01>;
			phandle = <0xc9>;
		};

		pcfg-pull-none-drv-level-2 {
			bias-disable;
			drive-strength = <0x02>;
			phandle = <0xcc>;
		};

		pcfg-pull-none-drv-level-3 {
			bias-disable;
			drive-strength = <0x03>;
			phandle = <0xca>;
		};

		pcfg-pull-none-drv-level-4 {
			bias-disable;
			drive-strength = <0x04>;
			phandle = <0xcb>;
		};

		pcfg-pull-up-drv-level-2 {
			bias-pull-up;
			drive-strength = <0x02>;
			phandle = <0xc5>;
		};

		pcfg-pull-none-smt {
			bias-disable;
			input-schmitt-enable;
			phandle = <0xc6>;
		};

		pcfg-output-low {
			output-low;
			phandle = <0xc8>;
		};

		cam {

			camm0-clk0-out {
				rockchip,pins = <0x03 0x0a 0x02 0xc4>;
				phandle = <0xb3>;
			};
		};

		dsm {

			dsm-pins {
				rockchip,pins = <0x01 0x0c 0x05 0xc4 0x01 0x0b 0x05 0xc4 0x01 0x0e 0x06 0xc4 0x01 0x0d 0x06 0xc4>;
				phandle = <0xa7>;
			};
		};

		eth {

			ethm1-pins {
				rockchip,pins = <0x02 0x01 0x02 0xc4>;
				phandle = <0xc0>;
			};
		};

		i2c0 {

			i2c0-xfer {
				rockchip,pins = <0x00 0x09 0x01 0xc6 0x00 0x0a 0x01 0xc6>;
				phandle = <0x3c>;
			};
		};

		i2c1 {

			i2c1m0-xfer {
				rockchip,pins = <0x00 0x0b 0x01 0xc6 0x00 0x0c 0x01 0xc6>;
				phandle = <0xae>;
			};
		};

		i2c2 {

			i2c2m0-xfer {
				rockchip,pins = <0x00 0x0d 0x01 0xc6 0x00 0x0e 0x01 0xc6>;
				phandle = <0xaf>;
			};
		};

		i2c3 {

			i2c3m0-xfer {
				rockchip,pins = <0x03 0x00 0x01 0xc6 0x03 0x01 0x01 0xc6>;
				phandle = <0xb0>;
			};
		};

		i2c4 {

			i2c4m0-xfer {
				rockchip,pins = <0x03 0x0e 0x05 0xc6 0x03 0x0f 0x05 0xc6>;
				phandle = <0xb2>;
			};
		};

		i2c5 {

			i2c5m0-xfer {
				rockchip,pins = <0x03 0x12 0x01 0xc6 0x03 0x13 0x01 0xc6>;
				phandle = <0xb7>;
			};
		};

		i2s0 {

			i2s0m0-lrck {
				rockchip,pins = <0x03 0x04 0x01 0xc4>;
				phandle = <0x8e>;
			};

			i2s0m0-mclk {
				rockchip,pins = <0x03 0x02 0x01 0xc4>;
				phandle = <0x46>;
			};

			i2s0m0-sclk {
				rockchip,pins = <0x03 0x03 0x01 0xc4>;
				phandle = <0x8f>;
			};

			i2s0m0-sdi0 {
				rockchip,pins = <0x03 0x09 0x01 0xc4>;
				phandle = <0x90>;
			};

			i2s0m0-sdo0 {
				rockchip,pins = <0x03 0x05 0x01 0xc4>;
				phandle = <0x91>;
			};
		};

		i2s1 {

			i2s1m0-lrck {
				rockchip,pins = <0x03 0x16 0x02 0xc4>;
				phandle = <0x92>;
			};

			i2s1m0-sclk {
				rockchip,pins = <0x03 0x15 0x02 0xc4>;
				phandle = <0x93>;
			};

			i2s1m0-sdi0 {
				rockchip,pins = <0x03 0x18 0x02 0xc4>;
				phandle = <0x94>;
			};

			i2s1m0-sdi1 {
				rockchip,pins = <0x03 0x19 0x02 0xc4>;
				phandle = <0x95>;
			};

			i2s1m0-sdi2 {
				rockchip,pins = <0x03 0x1a 0x02 0xc4>;
				phandle = <0x96>;
			};

			i2s1m0-sdi3 {
				rockchip,pins = <0x03 0x1b 0x02 0xc4>;
				phandle = <0x97>;
			};

			i2s1m0-sdo0 {
				rockchip,pins = <0x03 0x17 0x02 0xc4>;
				phandle = <0x98>;
			};

			i2s1m0-sdo1 {
				rockchip,pins = <0x04 0x0c 0x02 0xc4>;
				phandle = <0x99>;
			};

			i2s1m0-sdo2 {
				rockchip,pins = <0x04 0x0d 0x02 0xc4>;
				phandle = <0x9a>;
			};

			i2s1m0-sdo3 {
				rockchip,pins = <0x04 0x0e 0x02 0xc4>;
				phandle = <0x9b>;
			};
		};

		i2s2 {

			i2s2m0-lrck {
				rockchip,pins = <0x01 0x1e 0x01 0xc4>;
				phandle = <0x9c>;
			};

			i2s2m0-sclk {
				rockchip,pins = <0x01 0x1d 0x01 0xc4>;
				phandle = <0x9d>;
			};

			i2s2m0-sdi {
				rockchip,pins = <0x02 0x00 0x01 0xc4>;
				phandle = <0x9e>;
			};

			i2s2m0-sdo {
				rockchip,pins = <0x01 0x1f 0x01 0xc4>;
				phandle = <0x9f>;
			};
		};

		pdm {

			pdmm0-clk0 {
				rockchip,pins = <0x03 0x06 0x03 0xc4>;
				phandle = <0xa0>;
			};

			pdmm0-clk1 {
				rockchip,pins = <0x03 0x02 0x03 0xc4>;
				phandle = <0xa1>;
			};

			pdmm0-sdi0 {
				rockchip,pins = <0x03 0x09 0x02 0xc4>;
				phandle = <0xa2>;
			};

			pdmm0-sdi1 {
				rockchip,pins = <0x03 0x08 0x03 0xc4>;
				phandle = <0xa3>;
			};

			pdmm0-sdi2 {
				rockchip,pins = <0x03 0x07 0x03 0xc4>;
				phandle = <0xa4>;
			};

			pdmm0-sdi3 {
				rockchip,pins = <0x03 0x00 0x03 0xc4>;
				phandle = <0xa5>;
			};
		};

		pmic {

			pmic-int {
				rockchip,pins = <0x00 0x03 0x00 0xc7>;
				phandle = <0x3e>;
			};

			soc-slppin-gpio {
				rockchip,pins = <0x00 0x02 0x00 0xc8>;
				phandle = <0x41>;
			};

			soc-slppin-slp {
				rockchip,pins = <0x00 0x02 0x01 0xc4>;
				phandle = <0x3f>;
			};
		};

		pwm0 {

			pwm0m0-pins {
				rockchip,pins = <0x00 0x13 0x02 0xc9>;
				phandle = <0x4b>;
			};
		};

		pwm1 {

			pwm1m0-pins {
				rockchip,pins = <0x00 0x14 0x02 0xc9>;
				phandle = <0x4c>;
			};
		};

		pwm2 {

			pwm2m0-pins {
				rockchip,pins = <0x00 0x15 0x02 0xc9>;
				phandle = <0x4d>;
			};
		};

		pwm3 {

			pwm3m0-pins {
				rockchip,pins = <0x00 0x07 0x01 0xc9>;
				phandle = <0x4e>;
			};
		};

		pwm4 {

			pwm4m0-pins {
				rockchip,pins = <0x00 0x0f 0x02 0xc9>;
				phandle = <0x7d>;
			};
		};

		pwm5 {

			pwm5m0-pins {
				rockchip,pins = <0x00 0x12 0x02 0xc9>;
				phandle = <0x7e>;
			};
		};

		pwm6 {

			pwm6m0-pins {
				rockchip,pins = <0x00 0x11 0x02 0xc9>;
				phandle = <0x7f>;
			};
		};

		pwm7 {

			pwm7m0-pins {
				rockchip,pins = <0x00 0x10 0x02 0xc9>;
				phandle = <0x80>;
			};
		};

		pwm8 {

			pwm8m0-pins {
				rockchip,pins = <0x03 0x04 0x02 0xc9>;
				phandle = <0x81>;
			};
		};

		pwm9 {

			pwm9m0-pins {
				rockchip,pins = <0x03 0x05 0x02 0xc9>;
				phandle = <0x82>;
			};
		};

		pwm10 {

			pwm10m0-pins {
				rockchip,pins = <0x01 0x0d 0x05 0xc9>;
				phandle = <0x83>;
			};
		};

		pwm11 {

			pwm11m0-pins {
				rockchip,pins = <0x01 0x0e 0x05 0xc9>;
				phandle = <0x84>;
			};
		};

		pwm12 {

			pwm12m0-pins {
				rockchip,pins = <0x04 0x01 0x04 0xc9>;
				phandle = <0x85>;
			};
		};

		pwm13 {

			pwm13m0-pins {
				rockchip,pins = <0x04 0x04 0x03 0xc9>;
				phandle = <0x86>;
			};
		};

		pwm14 {

			pwm14m0-pins {
				rockchip,pins = <0x03 0x15 0x04 0xc9>;
				phandle = <0x87>;
			};
		};

		pwm15 {

			pwm15m0-pins {
				rockchip,pins = <0x03 0x16 0x04 0xc9>;
				phandle = <0x88>;
			};
		};

		rgmii {

			rgmiim1-miim {
				rockchip,pins = <0x01 0x17 0x02 0xc4 0x01 0x18 0x02 0xc4>;
				phandle = <0xbb>;
			};

			rgmiim1-rx_bus2 {
				rockchip,pins = <0x01 0x1c 0x02 0xc4 0x01 0x1f 0x02 0xc4 0x01 0x1e 0x02 0xc4>;
				phandle = <0xbd>;
			};

			rgmiim1-tx_bus2 {
				rockchip,pins = <0x01 0x19 0x02 0xc4 0x01 0x1a 0x02 0xc4 0x01 0x1b 0x02 0xc4>;
				phandle = <0xbc>;
			};

			rgmiim1-rgmii_clk {
				rockchip,pins = <0x01 0x16 0x02 0xc4 0x01 0x13 0x02 0xc4>;
				phandle = <0xbe>;
			};

			rgmiim1-rgmii_bus {
				rockchip,pins = <0x01 0x14 0x02 0xc4 0x01 0x15 0x02 0xc4 0x01 0x11 0x02 0xc4 0x01 0x12 0x02 0xc4>;
				phandle = <0xbf>;
			};

			rgmiim1-clk {
				rockchip,pins = <0x01 0x1d 0x02 0xc4>;
				phandle = <0xc1>;
			};
		};

		sdmmc0 {

			sdmmc0-bus4 {
				rockchip,pins = <0x01 0x0b 0x01 0xc5 0x01 0x0c 0x01 0xc5 0x01 0x0d 0x01 0xc5 0x01 0x0e 0x01 0xc5>;
				phandle = <0xaa>;
			};

			sdmmc0-clk {
				rockchip,pins = <0x01 0x10 0x01 0xc5>;
				phandle = <0xab>;
			};

			sdmmc0-cmd {
				rockchip,pins = <0x01 0x0f 0x01 0xc5>;
				phandle = <0xac>;
			};

			sdmmc0-det {
				rockchip,pins = <0x00 0x04 0x01 0xc7>;
				phandle = <0xad>;
			};
		};

		spdif {

			spdifm0-pins {
				rockchip,pins = <0x03 0x01 0x03 0xc4>;
				phandle = <0xa6>;
			};
		};

		spi0 {

			spi0m0-pins {
				rockchip,pins = <0x00 0x13 0x03 0xca 0x00 0x15 0x03 0xca 0x00 0x14 0x03 0xca>;
				phandle = <0x4a>;
			};

			spi0m0-csn0 {
				rockchip,pins = <0x00 0x12 0x03 0xca>;
				phandle = <0x48>;
			};

			spi0m0-csn1 {
				rockchip,pins = <0x00 0x0f 0x01 0xca>;
				phandle = <0x49>;
			};
		};

		spi1 {

			spi1m0-pins {
				rockchip,pins = <0x03 0x1e 0x04 0xca 0x04 0x03 0x04 0xca 0x04 0x02 0x04 0xca>;
				phandle = <0x78>;
			};

			spi1m0-csn0 {
				rockchip,pins = <0x03 0x1f 0x04 0xca>;
				phandle = <0x76>;
			};

			spi1m0-csn1 {
				rockchip,pins = <0x04 0x00 0x04 0xca>;
				phandle = <0x77>;
			};
		};

		spi2 {

			spi2m0-pins {
				rockchip,pins = <0x04 0x0e 0x04 0xca 0x03 0x1a 0x04 0xca 0x03 0x1b 0x04 0xca>;
				phandle = <0x7b>;
			};

			spi2m0-csn0 {
				rockchip,pins = <0x04 0x0d 0x04 0xca>;
				phandle = <0x79>;
			};

			spi2m0-csn1 {
				rockchip,pins = <0x04 0x0c 0x04 0xca>;
				phandle = <0x7a>;
			};
		};

		uart0 {

			uart0m0-xfer {
				rockchip,pins = <0x00 0x18 0x01 0xc7 0x00 0x19 0x01 0xc7>;
				phandle = <0xd0>;
			};
		};

		uart9 {

			uart9m0-xfer {
				rockchip,pins = <0x04 0x0b 0x03 0xc7 0x04 0x0a 0x03 0xc7>;
				phandle = <0x7c>;
			};
		};

		vo {

			vo-pins {
				rockchip,pins = <0x04 0x0f 0x01 0xc4 0x03 0x14 0x01 0xc4 0x03 0x15 0x01 0xc4 0x03 0x16 0x01 0xc4 0x03 0x17 0x01 0xc4 0x03 0x18 0x01 0xc4 0x03 0x19 0x01 0xc4 0x03 0x1a 0x01 0xc4 0x03 0x1b 0x01 0xc4 0x03 0x1c 0x01 0xc4 0x03 0x1d 0x01 0xc4 0x03 0x1e 0x01 0xc4 0x03 0x1f 0x01 0xc4 0x04 0x00 0x01 0xc4 0x04 0x01 0x01 0xc4 0x04 0x02 0x01 0xc4 0x04 0x03 0x01 0xc4 0x04 0x0e 0x01 0xc4 0x04 0x0c 0x01 0xc4 0x04 0x0d 0x01 0xc4>;
				phandle = <0x3b>;
			};
		};

		lcd {

			lcd-rst-gpio {
				rockchip,pins = <0x00 0x07 0x00 0xc4>;
			};
		};

		usb {

			usb-host-pwren {
				rockchip,pins = <0x00 0x06 0x00 0xc4>;
				phandle = <0xcd>;
			};
		};

		hym8563 {

			hym8563-int {
				rockchip,pins = <0x00 0x05 0x00 0xc7>;
				phandle = <0xb1>;
			};
		};

		headphone {

			hp-det {
				rockchip,pins = <0x00 0x00 0x00 0xc4>;
				phandle = <0xd9>;
			};
		};
	};

	rockchip-suspend {
		compatible = "rockchip,pm-rk3562";
		status = "disabled";
		rockchip,sleep-debug-en = <0x01>;
		rockchip,sleep-mode-config = <0x5e2>;
		rockchip,wakeup-config = <0x10>;
	};

	vcc5v0-sys {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_sys";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <0x4c4b40>;
		regulator-max-microvolt = <0x4c4b40>;
		phandle = <0xcf>;
	};

	vcc5v0-usb-host {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_usb_host";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <0x4c4b40>;
		regulator-max-microvolt = <0x4c4b40>;
		enable-active-high;
		gpio = <0x3d 0x06 0x00>;
		pinctrl-names = "default";
		pinctrl-0 = <0xcd>;
		phandle = <0x8b>;
	};

	vcc3v3-lcd0-n {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3_lcd0_n";
		regulator-boot-on;
		enable-active-high;
		gpio = <0x3d 0x08 0x00>;
		phandle = <0xd3>;
	};

	vcc-sys {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3_sys";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <0x325aa0>;
		regulator-max-microvolt = <0x325aa0>;
		phandle = <0x44>;
	};

	vdd-npu {
		compatible = "pwm-regulator";
		pwms = <0xce 0x00 0x1388 0x01>;
		regulator-name = "vdd_npu";
		regulator-min-microvolt = <0xc3500>;
		regulator-max-microvolt = <0x10c8e0>;
		regulator-init-microvolt = <0xdbba0>;
		regulator-always-on;
		regulator-boot-on;
		regulator-settling-time-up-us = <0xfa>;
		pwm-supply = <0xcf>;
		status = "okay";
		phandle = <0x5d>;
	};

	chosen {
		stdout-path = "/serial@ff210000";
		bootargs = "storagemedia=emmc androidboot.storagemedia=emmc 
		androidboot.mode=normal  androidboot.verifiedbootstate=orange 
		androidboot.serialno=cfe3e853975b5c4 earlycon=uart8250,mmio32,0xff210000 console=ttyS0,115200 rw rootwait root=PARTLABEL=rootfs systemd.unified_cgroup_hierarchy=0 ioslcpus=2,3 isolcpus=2,3 rcu_nocbs=2,3 rcu_nocb_poll=1024 irqaffinity=0,1  rcupdat.rcu_cpu_stall_suppress=1 acpi_irq_nobalance cpufreq.off=1 numa_balancing=disable nmi_watchdog=0 noht nosoftlocku";
	};

	fiq-debugger {
		compatible = "rockchip,fiq-debugger";
		rockchip,serial-id = <0x00>;
		rockchip,wake-irq = <0x00>;
		rockchip,irq-mode-enable = <0x01>;
		rockchip,baudrate = <0x1c200>;
		interrupts = <0x00 0xf2 0x04>;
		pinctrl-names = "default";
		pinctrl-0 = <0xd0>;
		status = "disabled";
	};

	backlight {
		compatible = "pwm-backlight";
		brightness-levels = <0x00 0x14 0x14 0x15 0x15 0x16 0x16 0x17 0x17 0x18 0x18 0x19 0x19 0x1a 0x1a 0x1b 0x1b 0x1c 0x1c 0x1d 0x1d 0x1e 0x1e 0x1f 0x1f 0x20 0x20 0x21 0x21 0x22 0x22 0x23 0x23 0x24 0x24 0x25 0x25 0x26 0x26 0x27 0x28 0x29 0x2a 0x2b 0x2c 0x2d 0x2e 0x2f 0x30 0x31 0x32 0x33 0x34 0x35 0x36 0x37 0x38 0x39 0x3a 0x3b 0x3c 0x3d 0x3e 0x3f 0x40 0x41 0x42 0x43 0x44 0x45 0x46 0x47 0x48 0x49 0x4a 0x4b 0x4c 0x4d 0x4e 0x4f 0x50 0x51 0x52 0x53 0x54 0x55 0x56 0x57 0x58 0x59 0x5a 0x5b 0x5c 0x5d 0x5e 0x5f 0x60 0x61 0x62 0x63 0x64 0x65 0x66 0x67 0x68 0x69 0x6a 0x6b 0x6c 0x6d 0x6e 0x6f 0x70 0x71 0x72 0x73 0x74 0x75 0x76 0x77 0x78 0x79 0x7a 0x7b 0x7c 0x7d 0x7e 0x7f 0x80 0x81 0x82 0x83 0x84 0x85 0x86 0x87 0x88 0x89 0x8a 0x8b 0x8c 0x8d 0x8e 0x8f 0x90 0x91 0x92 0x93 0x94 0x95 0x96 0x97 0x98 0x99 0x9a 0x9b 0x9c 0x9d 0x9e 0x9f 0xa0 0xa1 0xa2 0xa3 0xa4 0xa5 0xa6 0xa7 0xa8 0xa9 0xaa 0xab 0xac 0xad 0xae 0xaf 0xb0 0xb1 0xb2 0xb3 0xb4 0xb5 0xb6 0xb7 0xb8 0xb9 0xba 0xbb 0xbc 0xbd 0xbe 0xbf 0xc0 0xc1 0xc2 0xc3 0xc4 0xc5 0xc6 0xc7 0xc8 0xc9 0xca 0xcb 0xcc 0xcd 0xce 0xcf 0xd0 0xd1 0xd2 0xd3 0xd4 0xd5 0xd6 0xd7 0xd8 0xd9 0xda 0xdb 0xdc 0xdd 0xde 0xdf 0xe0 0xe1 0xe2 0xe3 0xe4 0xe5 0xe6 0xe7 0xe8 0xe9 0xea 0xeb 0xec 0xed 0xee 0xef 0xf0 0xf1 0xf2 0xf3 0xf4 0xf5 0xf6 0xf7 0xf8 0xf9 0xfa 0xfb 0xfc 0xfd 0xfe 0xff>;
		default-brightness-level = <0xc8>;
		pwms = <0xd1 0x00 0x61a8 0x00>;
		status = "okay";
		phandle = <0xd2>;
	};

	panel {
		compatible = "simple-panel";
		status = "okay";
		backlight = <0xd2>;
		power-supply = <0xd3>;
		enable-delay-ms = <0x14>;
		prepare-delay-ms = <0x14>;
		unprepare-delay-ms = <0x14>;
		disable-delay-ms = <0x14>;
		width-mm = <0xd9>;
		height-mm = <0x88>;
		bus-format = <0x1010>;

		display-timings {
			native-mode = <0xd4>;

			lvds-timing0 {
				clock-frequency = <0x4491b60>;
				hactive = <0x320>;
				vactive = <0x500>;
				hback-porch = <0x18>;
				hfront-porch = <0x48>;
				vback-porch = <0x0a>;
				vfront-porch = <0x0c>;
				hsync-len = <0x18>;
				vsync-len = <0x02>;
				hsync-active = <0x00>;
				vsync-active = <0x00>;
				de-active = <0x00>;
				pixelclk-active = <0x00>;
				phandle = <0xd4>;
			};
		};

		ports {
			#address-cells = <0x01>;
			#size-cells = <0x00>;

			port@0 {
				reg = <0x00>;
				dual-lvds-even-pixels;

				endpoint {
					remote-endpoint = <0xd5>;
					phandle = <0x3a>;
				};
			};
		};
	};

	panel-rgb {
		compatible = "simple-panel";
		status = "disabled";
		backlight = <0xd2>;
		power-supply = <0xd3>;
		reset-delay-ms = <0x14>;
		enable-delay-ms = <0x14>;
		prepare-delay-ms = <0x14>;
		unprepare-delay-ms = <0x14>;
		disable-delay-ms = <0x14>;
		width-mm = <0xa4>;
		height-mm = <0x64>;
	};

	adc-keys {
		compatible = "adc-keys";
		io-channels = <0xd6 0x01>;
		io-channel-names = "buttons";
		keyup-threshold-microvolt = <0x1b7740>;
		poll-interval = <0x64>;

		vol-up-key {
			linux,code = <0x73>;
			label = "volume up";
			press-threshold-microvolt = <0x4268>;
		};

		vol-down-key {
			linux,code = <0x72>;
			label = "volume down";
			press-threshold-microvolt = <0x65130>;
		};

		menu-key {
			linux,code = <0x8b>;
			label = "menu";
			press-threshold-microvolt = <0xc3500>;
		};

		back-key {
			linux,code = <0x9e>;
			label = "back";
			press-threshold-microvolt = <0x124f80>;
		};
	};

	rk809-sound {
		status = "okay";
		compatible = "rockchip,multicodecs-card";
		rockchip,card-name = "rockchip-rk809";
		hp-det-gpio = <0x3d 0x00 0x01>;
		rockchip,format = "i2s";
		rockchip,mclk-fs = <0x100>;
		rockchip,cpu = <0xd7>;
		rockchip,codec = <0xd8>;
		pinctrl-names = "default";
		pinctrl-0 = <0xd9>;
	};

	leds {
		compatible = "gpio-leds";

		work {
			gpios = <0xda 0x09 0x00>;
			linux,default-trigger = "none";
		};
	};
};
