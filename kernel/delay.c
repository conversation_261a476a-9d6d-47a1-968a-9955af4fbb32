/**
 * @file    kernel/delay.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 * 
 * 92c70e29 2026-07-11 posix_timer
 * c295493f 2024-07-02 移除include 路径中clock
 * ac006b61 2024-07-02 移除一级ttos目录
 * b041d869 2024-05-15 格式化代码并处理一些头文件依赖问题
 * 8ae4d067 2024-05-07 1.添加链接libgcc.a库 2.添加pipe支持 3.添加time event支持
 * 
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
*/

#include <errno.h>
#include <stdio.h>
#include <timer_event/timer.h>
#include <ttos_time.h>
#include <timer_event/waitqueue.h>
#include <time/ktime.h>

// clock_nanosleep系统调用使用
void time_event_sleep (const struct timespec64 *rqtp, struct timespec64 *rmtp)
{
    u64 expiry_ns = rqtp ->tv_nsec + rqtp->tv_sec * NANOSECOND_PER_SECOND;

    waitqueue_sleep_with_handler (expiry_ns, &waitqueue_timeout);
}
