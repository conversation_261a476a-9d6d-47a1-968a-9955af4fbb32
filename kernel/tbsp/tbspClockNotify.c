/**
 * @file    kernel/tbsp/tbspClockNotify.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 * 
 * 914d9323 2024-07-11 添加周期任务支持
 * c295493f 2024-07-02 移除include 路径中clock
 * ac006b61 2024-07-02 移除一级ttos目录
 * dbdaf4e3 2024-06-14 virtio net 接收
 * 98bd7a1c 2024-06-05 修复在arch timer初始化前调用时间获取接口导致系统卡死问题
 * a3a0e098 2024-06-05 update netdev
 * b041d869 2024-05-15 格式化代码并处理一些头文件依赖问题
 * f9f81129 2024-05-07 调整部分timer代码。
 * 8ae4d067 2024-05-07 1.添加链接libgcc.a库 2.添加pipe支持 3.添加time event支持
 * 27ecdd3c 2024-04-24 移除旧的posix接口相关代码
 * 4e3c1310 2024-04-15 对接部分syscall
 * 04b1f3b4 2024-04-15 统一cpuset接口
 * 33a84634 2024-04-11 添加musl库 移除标准库头文件屏蔽原posix相关头问题
 * 89405763 2024-03-27 移除日志中\n 全部修改为日志输出
 * 732c5b3d 2024-03-27 添加日志系统Klog
 * 9fa804c4 2024-03-22 同一编码格式为UTF-8
 * b84bd9f8 2024-03-19 删除临时函数，添加函数实现。
 * f3e1c5ab 2024-03-19 1.删除临时代码 2.添加kmalloc kfree的实现 3.tick中断handler逻辑微调
 * c7bbbfca 2024-03-18 提交任务功能模块
 * 
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
*/


/* @<MOD_INFO */
/*
 * @file: DC_RT_6.pdl
 * @brief:
 *      <li>本模块为TTOS的板级支持包，为TTOS提供硬件访问支持。</li>
 *      <li>TBSP的核心是处理虚拟中断，TTOS初始化时接管tick中断。</li>
 * @implements: DT.6
 */
/* @MOD_INFO> */

/************************头 文 件******************************/

/* @<MOD_HEAD */
#include <commonTypes.h>
#include <int.h>
#include <timer_event/timer.h>
#include <ttosBase.h>
#include <timer_event/waitqueue.h>
#include <time/ktime.h>

#define KLOG_TAG "Kernel"
#include <klog.h>

/* @MOD_HEAD> */

/************************宏 定 义******************************/
#ifndef NSEC_PER_SEC
#define NSEC_PER_SEC 1000000000UL
#endif
#ifndef USEC_PER_SEC
#define USEC_PER_SEC 1000000UL
#endif
#ifndef MSEC_PER_SEC
#define MSEC_PER_SEC 1000UL
#endif

#ifdef CONFIG_X86_64
#define CONFIG_SYS_TICK_NS (NSEC_PER_MSEC * 1000)
#else
#define CONFIG_SYS_TICK_NS (NSEC_PER_MSEC * 10)
#endif

/************************类型定义******************************/
/************************外部声明******************************/
INT32 ipiRescheduleEmit (cpu_set_t *cpus, T_BOOL selfexcluded);
void period_sched_group_notify (T_UWORD ticks);
/************************前向声明******************************/
T_EXTERN void tbspClockNotify (void);
/************************模块变量******************************/

T_MODULE T_BOOL   ttosGetSytemFreqFlag = FALSE;
T_MODULE T_UDWORD tscFreq              = 0U;
static struct timer_event wake_event;
/************************全局变量******************************/
/* @<MOD_VAR */
T_UWORD pwaitCurentTicks, pexpireCurrentTicks;

/* 系统tick计数值 */
T_MODULE T_UDWORD tickCount;

/* 分区tick计数值 */
T_MODULE T_UDWORD ttosTickCount;

/* 周期等待队列链上第一个周期任务的周期等待时间 */
T_UWORD pwaitTicks;

/*截止期等待队列链上第一个周期任务的截止期等待时间 */
T_UWORD pexpireTicks;

/*当前只有主核才处理虚拟tick中断。*/
cpu_set_t tickIsrIpiReschedulecpus;

/* @MOD_VAR> */

/************************实    现******************************/

/* @MODULE> */

/*
 * 当ttos运行在分区时，ticks数是一个相对Tick数，它是从SVMK中断派发当中传递
 * 过来的在SVMK调度分区、时间通知的过程中被计算出来。
 * 它用来表示，从当前分区主分区被切换出去到重新调度运行，DeltaTT总共流
 * 逝了多少Ticks数。
 */

/*
 * @brief:
 *    对任务进行时间的通知。
 * @param[in]: ticks: 从上次投递tick中断到本次投递tick中断期间当前分区主分区流逝
 * 的 tick数。
 * @return:
 *    无
 * @tracedREQ: RT.6
 * @implements: DT.6.8
 */
void tbspClockNotify (void)
{
    T_UWORD ticks = 1;
    /* @KEEP_COMMENT: 设置第一次进行时间通知的标志变量isFirstNotify为TRUE */
    T_MODULE T_BOOL isFirstNotify = TRUE;
    /* @KEEP_COMMENT: 设置周期等待通知到时剩余ticks变量pwaiteLeftTicks为0 */
    T_UWORD pwaiteLeftTicks = 0U;
    /* @KEEP_COMMENT:
     * 设置周期截止时间等待通知到时剩余ticks变量pexpireLeftTicks为0 */
    T_UWORD pexpireLeftTicks = 0U;
    /* @KEEP_COMMENT: 设置是否进行周期等待通知变量pwCurentTicksNotify为FALSE */
    T_BOOL pwCurentTicksNotify = FALSE;
    /* @KEEP_COMMENT:
     * 设置是否进行周期截止时间等待通知变量pexpireTicksNotify为FALSE */
    T_BOOL pexpireTicksNotify = FALSE;
    /* @KEEP_COMMENT:
     * 设置周期队列通知类型变量pQueueNotify为TTOS_PERIOD_NONE_QUEUE_NOTIFY */
    T_TTOS_PeriodQueueNotifyType pQueueNotify = TTOS_PERIOD_NONE_QUEUE_NOTIFY;

    /* @REPLACE_BRACKET: TRUE == isFirstNotify */
    if (TRUE == isFirstNotify)
    {
        /* @KEEP_COMMENT: 设置<ticks>为1 */
        ticks = 1U;
        /* @KEEP_CODE: */
        isFirstNotify = FALSE;
    }

    /* tick处理过程中是禁止中断的，所以此处不需要在禁止中断。*/
    TTOS_KERNEL_LOCK ();
    (void)ttosIncTickIntNestLevel ();
    tickCount += ticks;
    /* 记录真实投递给主分区的ticks，不加上流逝的ticks 。*/
    ttosTickCount++;
    /* @KEEP_COMMENT: 调用ttosTimerNotify(DT.4.10)实现定时器等待队列的时间通知
     */
    (void)ttosTimerNotify (ticks);
    /* @KEEP_COMMENT: 调用ttosTickNotify(DT.2.33)实现任务的tick时间通知 */
    (void)ttosTickNotify (ticks);

    (void)period_sched_group_notify(ticks);

    /* @REPLACE_BRACKET: 周期等待时间不为0 */
    if (pwaitCurentTicks != 0U)
    {
        /* @REPLACE_BRACKET: 周期等待时间大于<ticks> */
        if (pwaitCurentTicks > ticks)
        {
            /* @KEEP_COMMENT: 周期等待时间 = 周期等待时间 - <ticks> */
            pwaitCurentTicks -= ticks;
        }

        else
        {
            /* @KEEP_COMMENT: pwaiteLeftTicks = <ticks> - 周期等待通知时间 */
            pwaiteLeftTicks = ticks - pwaitCurentTicks;
            /* @KEEP_CODE: */
            pwCurentTicksNotify = TRUE;
            /* @KEEP_COMMENT: 设置周期等待时间为0 */
            pwaitCurentTicks = 0U;
            pwaitTicks       = 0U;
        }
    }

    /* @REPLACE_BRACKET: 截止期等待时间不为0 */
    if (pexpireCurrentTicks != 0U)
    {
        /* @REPLACE_BRACKET: 截止期等待时间大于<ticks> */
        if (pexpireCurrentTicks > ticks)
        {
            /* @KEEP_COMMENT: 截止期等待时间 = 截止期等待时间 - <ticks> */
            pexpireCurrentTicks -= ticks;
        }

        else
        {
            /* @KEEP_COMMENT: pexpireLeftTicks = <ticks> - 截止期等待通知时间 */
            pexpireLeftTicks = ticks - pexpireCurrentTicks;
            /* @KEEP_CODE: */
            pexpireTicksNotify = TRUE;
            /* @KEEP_COMMENT: 设置截止期等待时间为0 */
            pexpireCurrentTicks = 0U;
            pexpireTicks        = 0U;
        }
    }

    /* @REPLACE_BRACKET: TRUE == pwCurentTicksNotify */
    if (TRUE == pwCurentTicksNotify)
    {
        /*使用剩余时间来处理周期等待队列上的周期任务在绝对时间上应该处于的状态*/
        /* @KEEP_COMMENT: 调用ttosPeriodWaitQueueModify(DT.2.28)
         * 根据pwaiteLeftTicks来更新周期任务周期等待的时间 */
        (void)ttosPeriodWaitQueueModify (pwaiteLeftTicks);
    }

    /* @REPLACE_BRACKET: TRUE == pexpireTicksNotify */
    if (TRUE == pexpireTicksNotify)
    {
        /*使用剩余时间来处理截止期等待队列上的周期任务在绝对时间上应该处于的状态*/
        /* @KEEP_COMMENT: 调用ttosPeriodExpireQueueModify(DT.2.29)
         * 根据pexpireLeftTicks来更新周期任务截止期等待的时间 */
        (void)ttosPeriodExpireQueueModify (pexpireLeftTicks);
    }

    /* @REPLACE_BRACKET: (TRUE == pwCurentTicksNotify)||(TRUE ==
     * pexpireTicksNotify) */
    if ((TRUE == pwCurentTicksNotify) || (TRUE == pexpireTicksNotify))
    {
        /*
         * @KEEP_COMMENT: 调用ttosPeriodQueueMerge(DT.2.30)
         * 合并临时队列的周期任务到周期或者 截止时间等待队列上
         * ，并获取队列通知类型存放至pQueueNotify
         */
        pQueueNotify = ttosPeriodQueueMerge ();
    }

    /* @REPLACE_BRACKET: TRUE == pQueueNotify */
    switch (pQueueNotify)
    {
    case TTOS_PERIOD_WAIT_QUEUE_NOTIFY:
        /*
         * @KEEP_COMMENT: 调用ttosPeriodWaitNotify(DT.2.31)实现周期任务周期等待
         * 队列的时间通知
         */
        (void)ttosPeriodWaitNotify ();
        break;

    case TTOS_PERIOD_EXPIRE_QUEUE_NOTIFY:
        /*
         * @KEEP_COMMENT: 调用ttosPeriodExpireNotify(DT.2.27)实现周期任务截止时
         * 间等待队列的时间通知
         */
        (void)ttosPeriodExpireNotify ();
        break;

    case TTOS_PERIOD_ALL_QUEUE_NOTIFY:
        /*
         * @KEEP_COMMENT: 调用ttosPeriodWaitNotify(DT.2.31)实现周期任务周期等待
         * 队列的时间通知和调用ttosPeriodExpireNotify(DT.2.27)实现周期任务截止时
         * 间等待队列的时间通知
         */
        (void)ttosPeriodWaitNotify ();
        (void)ttosPeriodExpireNotify ();
        break;

    default:
        /* default */
        break;
    }

    ttosPeriodPrioReadyQueueSetReady();
    (void)ttosDecTickIntNestLevel ();
    TTOS_KERNEL_UNLOCK ();
}
/* @IGNORE_BEGIN: */
/**
 * @brief:
 *    获取系统tick数。
 * @param[out]: ticks: 存放系统tick的变量
 * @return:
 *    TTOS_OK:获取系统tick成功。
 * @tracedREQ:
 * @implements:
 */
T_TTOS_ReturnCode TTOS_GetSystemTicks (T_UDWORD *ticks)
{
    *ticks = tickCount;
    return TTOS_OK;
}

/*
 * @brief:
 *    获取系统tick数。
 * @return:
 *    系统tick数。
 */
T_UDWORD ttosGetSystemTicks (void)
{
    return (tickCount);
}

/**
 * @brief:
 *    获取分区运行tick数。
 * @param[out]: ticks: 存放分区运行tick的变量
 * @return:
 *    TTOS_OK:获取分区运行tick成功。
 * @tracedREQ:
 * @implements:
 */
T_TTOS_ReturnCode TTOS_GetRunningTicks (T_UDWORD *ticks)
{
    *ticks = ttosTickCount * cpuEnabledNumGet ();
    return TTOS_OK;
}

/**
 * @brief:
 *    获取分区在一个CPU上运行的tick数。
 * @param[out]: ticks: 存放分区运行tick的变量
 * @return:
 *    TTOS_OK:获取分区运行tick成功。
 * @tracedREQ:
 * @implements:
 */
T_TTOS_ReturnCode TTOS_GetOneCpuRunningTicks (T_UDWORD *ticks)
{
    *ticks = ttosTickCount;
    return TTOS_OK;
}

/**
 * @brief:
 *    获取每秒对应的tick数
 * @return:
 *    返回每秒tick数
 * @tracedREQ:
 * @implements:
 */
T_UWORD TTOS_GetSysClkRate (void)
{
    return NSEC_PER_SEC / CONFIG_SYS_TICK_NS;
}

/* @IGNORE_END: */
/*
 * @brief:
 *    设置周期等待队列的通知时间。
 * @param[in]: ticks: 周期等待队列的通知时间
 * @return:
 *    无
 * @tracedREQ: RT.6
 * @implements: DT.6.9
 */
void tbspSetPWaitTick (T_UWORD ticks)
{
    /* @KEEP_COMMENT: 设置周期等待队列通知时间为<ticks> */
    pwaitTicks = ticks;
    /* @KEEP_COMMENT: 设置周期等待队列等待时间为<ticks> */
    pwaitCurentTicks = ticks;
}

/*
 * @brief:
 *    设置截止期等待队列的通知时间。
 * @param[in]: ticks: 截止期等待队列的通知时间
 * @return:
 *    无
 * @tracedREQ: RT.6
 * @implements: DT.6.10
 */
void tbspSetPExpireTick (T_UWORD ticks)
{
    /* @KEEP_COMMENT: 设置截止期等待队列通知时间为<ticks> */
    pexpireTicks = ticks;
    /* @KEEP_COMMENT: 设置截止期等待队列等待时间为<ticks> */
    pexpireCurrentTicks = ticks;
}

/*
 * @brief:
 *    获取周期等待队列已等待的时间。
 * @return:
 *    周期等待队列已等待的时间
 * @tracedREQ: RT.6
 * @implements: DT.6.11
 */
T_UWORD tbspGetPWaitTick (void)
{
    /* @REPLACE_BRACKET: 周期等待队列通知时间减去周期等待队列等待时间 */
    return (pwaitTicks - pwaitCurentTicks);
}

/*
 * @brief:
 *    获取截止期等待队列已等待的时间。
 * @return:
 *    截止期等待队列已等待的时间
 * @tracedREQ: RT.6
 * @implements: DT.6.12
 */
T_UWORD tbspGetPExpireTick (void)
{
    /* @REPLACE_BRACKET: 截止期等待队列通知时间减去截止期等待队列等待时间 */
    return (pexpireTicks - pexpireCurrentTicks);
}

/*
 * @brief:
 *    系统定时器处理程序。
 * @return:
 *    无
 */

void ttos_sys_tick_handler (struct timer_event *event)
{
    tbspClockNotify ();
}

/*
 * @brief:
 *    系统TICK初始化。
 * @return:
 *    无
 */

void ttos_sys_tick_init (void)
{
    /* 设置TICK中断handler */
    INIT_TIMER_EVENT(&wake_event, ttos_sys_tick_handler, NULL);

    /* 设置周期为CONFIG_SYS_TICK_NS */     
    timer_event_periodic_config(&wake_event, TTOS_TIMER_REPEAT_FOREVER, CONFIG_SYS_TICK_NS);

    /* 设置10us后第一次触发 */
    timer_event_start(&wake_event, NSEC_PER_USEC*10, NULL);
}

/*
 * @brief:
 *    初始化虚拟tick中断。
 * @return:
 *    无
 * @tracedREQ: RT.6
 * @implements: DT.6.13
 */
T_VOID tbspInitTimer (T_VOID)
{
    ttos_sys_tick_init();
}

/* @END_HERE: */
/*
 * @brief:
 *    获取当前系统中流逝的TSC COUNT数。
 * @return:
 *    当前系统中流逝的TSC COUNT数。
 */
T_UDWORD TTOS_GetCurrentSystemCount (void)
{
    T_UDWORD count = 0U;

    count = timer_clocksource_count ();
    return count;
}

/*
 * @brief:
 *    获取系统中TSC频率。
 * @return:
 *    系统中TSC频率。
 */
T_UDWORD TTOS_GetCurrentSystemFreq (void)
{
    T_UDWORD freq = 0U;

    if (FALSE == ttosGetSytemFreqFlag)
    {
        tscFreq              = timer_clocksource_frequency ();
        ttosGetSytemFreqFlag = TRUE;
    }

    freq = tscFreq;
    return freq;
}

/*
 * @brief:
 *    获取TSC COUNT对应流逝的时间。
 * @param[in]: count: TSC COUNT数
 * @param[in]: timeUnitType: 时间单位
 * @return:
 *    系统从启动流逝的时间。
 */
T_UDWORD TTOS_GetCurrentSystemTime (T_UDWORD            count,
                                    T_TTOS_TimeUnitType timeUnitType)
{
    T_UDWORD elapsed_time = 0ULL;

    if (FALSE == ttosGetSytemFreqFlag)
    {
        tscFreq              = TTOS_GetCurrentSystemFreq ();
        if(tscFreq > 0)
        {
            ttosGetSytemFreqFlag = TRUE;
        }
    }
    if (tscFreq > 0U)
    {
        switch (timeUnitType)
        {
        case TTOS_NS_UNIT:
            elapsed_time = (T_UDWORD)(count * (1.0 * NSEC_PER_SEC / tscFreq));
            break;

        case TTOS_US_UNIT:
            elapsed_time = (T_UDWORD)(count * (1.0 * USEC_PER_SEC / tscFreq));
            break;

        case TTOS_MS_UNIT:
            elapsed_time = (T_UDWORD)(count * (1.0 * MSEC_PER_SEC / tscFreq));
            break;

        default:
            elapsed_time = (T_UDWORD)(count / tscFreq);
            break;
        }
    }

    return elapsed_time;
}

/*
 * @brief:
 *    获取两个时间点的间隔时间
 * @param[in]: count1: 第一个时间点的TSC COUNT数
 * @param[in]: count2: 第二个时间点的TSC COUNT数
 * @param[in]: timeUnitType: 时间单位
 * @return:
 *    两个时间点的间隔时间
 */
T_UDWORD TTOS_GetCurrentSystemSubTime (T_UDWORD count1, T_UDWORD count2,
                                       T_TTOS_TimeUnitType timeUnitType)
{
    T_UDWORD interval_time = 0ULL;

    if (FALSE == ttosGetSytemFreqFlag)
    {
        tscFreq              = TTOS_GetCurrentSystemFreq ();
        ttosGetSytemFreqFlag = TRUE;
    }

    if (tscFreq != 0U)
    {
        switch (timeUnitType)
        {
        case TTOS_NS_UNIT:
            interval_time = (T_UDWORD)((count2 - count1)
                                       * (1.0 * NSEC_PER_SEC / tscFreq));
            break;

        case TTOS_US_UNIT:
            interval_time = (T_UDWORD)((count2 - count1)
                                       * (1.0 * USEC_PER_SEC / tscFreq));
            break;

        case TTOS_MS_UNIT:
            interval_time = (T_UDWORD)((count2 - count1)
                                       * (1.0 * MSEC_PER_SEC / tscFreq));
            break;

        default:
            interval_time = (T_UDWORD)((count2 - count1) * 1.0 / tscFreq);
            break;
        }
    }

    return interval_time;
}

unsigned long long get_sys_tick_ns (void)
{
    return CONFIG_SYS_TICK_NS;
}
