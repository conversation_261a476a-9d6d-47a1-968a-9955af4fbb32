cmake_minimum_required(VERSION 3.19)

# 处理版本标签
if(VERSION_TAG)
    add_definitions(-DVERSION_TAG="${VERSION_TAG}")
endif()

if(BUILD_ENV)
    set(BUILD_ENV_PATH "${BUILD_ENV}")
else()
    set(BUILD_ENV_PATH "${CMAKE_SOURCE_DIR}/build-env")
endif()

include(${BUILD_ENV_PATH}/cmake/top.cmake)

if(CONFIG_PROJECT_NAME)
project(${CONFIG_PROJECT_NAME} C ASM CXX)
else()
project(rtos C ASM CXX)
endif()

list(APPEND CMAKE_MODULE_PATH "${BUILD_ENV_PATH}/cmake")

include(UseSphinxDoc)

add_sphinx_doc(
  SOURCE_DIR
    ${CMAKE_SOURCE_DIR}/doc
  OUT_DIR
    ${CMAKE_CURRENT_BINARY_DIR}/sphinx_html
  TARGET_NAME
    docs
  DOC_TYPE
     html
  COMMENT
    "HTML documentation"
  )

add_sphinx_doc(
  SOURCE_DIR
    ${CMAKE_SOURCE_DIR}/doc
  OUT_DIR
    ${CMAKE_CURRENT_BINARY_DIR}/sphinx_html
  TARGET_NAME
    pdfdocs
  DOC_TYPE
     latexpdf
  COMMENT
    "PDF documentation"
  )
set(CMAKE_EXECUTABLE_SUFFIX ".elf")
set(CMAKE_VERBOSE_MAKEFILE OFF)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
# Re-configure (Re-execute all CMakeLists.txt code) when autoconf.h changes
set_property(GLOBAL APPEND PROPERTY CMAKE_CONFIGURE_DEPENDS ${CONFIG_C_HEADER})

set(GLOB_SOURCE_LIST "")
set(GLOB_INC_PATH_LIST "")
set(GLOB_LIB_PATH_LIST "")
set(GLOB_LIB_LIST "")
set(GLOB_LINKSCRIPT "")
set(GLOBA_DESC_TEMPLATE  ${BUILD_ENV_PATH}/desc_template/desc_template)

set(LIBC_INC_PATH ${CMAKE_BINARY_DIR}/libk/include)
set(KERNEL_INC_PATH ${CMAKE_SOURCE_DIR}/include)
set(LWIP_INC_PATH ${CMAKE_BINARY_DIR}/liblwip/include)

#设置asm_offset头文件路径
set(ASM_OFFSET_INC_PATH ${CMAKE_BINARY_DIR}/arch/${CONFIG_ARCH})
#设置asm_offset 源文件路径
set(ASM_OFFSET_SRC_FILE ${CMAKE_SOURCE_DIR}/arch/${CONFIG_ARCH}/asm-offsets.c)
#设置asm_offset目的文件路径
set(ASM_OFFSET_DEST_PATH ${CMAKE_BINARY_DIR}/arch/${CONFIG_ARCH})

list(APPEND GLOB_INC_PATH_LIST ${LIBC_INC_PATH})
list(APPEND GLOB_INC_PATH_LIST ${KERNEL_INC_PATH})
list(APPEND GLOB_INC_PATH_LIST ${KERNEL_INC_PATH}/arch/${CONFIG_ARCH})
list(APPEND GLOB_INC_PATH_LIST ${CMAKE_SOURCE_DIR}/include/linux-comp)
list(APPEND GLOB_INC_PATH_LIST ${ASM_OFFSET_INC_PATH})

add_executable(${PROJECT_NAME})

# 包含子文件夹
SUBDIRLIST(SUBDIRS)

foreach(SUB_FOLDER ${SUBDIRS})
    ADD_SUBDIR(${SUB_FOLDER})
endforeach()

foreach(inc_path ${GLOB_INC_PATH_LIST})
    include_directories(${inc_path})
endforeach()


# 单独编译gen_init_cpio，用于制作cpio格式的initramfs
# todo 如果需要编译阶段生成cpio，在这里单独处理



target_sources(${PROJECT_NAME} PRIVATE ${GLOB_SOURCE_LIST})

add_custom_command(
  OUTPUT ${CMAKE_BINARY_DIR}/link.lds
  COMMAND cp -f ${GLOB_LINKSCRIPT} ${CMAKE_BINARY_DIR}/link.c
  COMMAND ${CMAKE_C_COMPILER} -include ${CONFIG_C_HEADER} -include ${CONFIG_LDS_HEADER} -E ${CMAKE_BINARY_DIR}/link.c -o ${CMAKE_BINARY_DIR}/link.lds
  COMMAND sed -i "/^#/d" ${CMAKE_BINARY_DIR}/link.lds
)


add_custom_target(lds
  DEPENDS ${CMAKE_BINARY_DIR}/link.lds
  COMMENT "Creating link script"
)
set_property(TARGET lds APPEND PROPERTY ADDITIONAL_CLEAN_FILES ${CMAKE_BINARY_DIR}/link.c)
set_property(TARGET lds APPEND PROPERTY ADDITIONAL_CLEAN_FILES ${CMAKE_BINARY_DIR}/link.lds)

if(NOT CONFIG_MODULES)
  set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,-gc-sections")
endif()

if(CONFIG_ARCH_X86_64)
	set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,-X,-N,--no-dynamic-linker -no-pie -Wl,--build-id=none,-Map,${CMAKE_BINARY_DIR}/${PROJECT_NAME}.map -T${CMAKE_BINARY_DIR}/link.lds  -mcmodel=large")
else()
  set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,--build-id=none,-Map,${CMAKE_BINARY_DIR}/${PROJECT_NAME}.map -T${CMAKE_BINARY_DIR}/link.lds")
endif()
#设置包含路径
set(ASM_OFFSET_INCLUDE_FILE "")
foreach(dir ${GLOB_INC_PATH_LIST})
  list(APPEND ASM_OFFSET_INCLUDE_FILE "-I${dir}")
endforeach()

if(CONFIG_TOOLCHAIN_CLANG)
set(ASMOFFSET_C_FLAGS  -target ${CC_TARGET})
endif(CONFIG_TOOLCHAIN_CLANG)

add_custom_command(
  OUTPUT ${ASM_OFFSET_DEST_PATH}/asm-offsets.h
  COMMAND ${CMAKE_C_COMPILER}  ${ASM_OFFSET_INCLUDE_FILE} -S -nostdinc ${ASMOFFSET_C_FLAGS} ${ASM_OFFSET_SRC_FILE} -o ${ASM_OFFSET_DEST_PATH}/asm-offsets.S
  COMMAND chmod +x ${BUILD_ENV_PATH}/tools/asm_offset.sh
  COMMAND ${BUILD_ENV_PATH}/tools/asm_offset.sh ${ASM_OFFSET_DEST_PATH}/asm-offsets.S ${ASM_OFFSET_DEST_PATH}/asm-offsets.h
  DEPENDS ${ASM_OFFSET_SRC_FILE}  ${BUILD_ENV_PATH}/tools/asm_offset.sh
)

if(CONFIG_ALLSYMS)
# 使用不依赖预构建ELF的方法实现
# 阶段1: 创建一个最小的allsyms.c文件作为初始符号表
file(WRITE ${CMAKE_BINARY_DIR}/empty_allsyms.c "#include <symtab.h>\n\nstruct symtab_item g_allsyms[] = {};\nint g_nallsyms = 0;\n")

add_custom_command(
    OUTPUT ${CMAKE_BINARY_DIR}/allsyms.c
    COMMAND ${CMAKE_COMMAND} -E copy_if_different ${CMAKE_BINARY_DIR}/empty_allsyms.c ${CMAKE_BINARY_DIR}/allsyms.c
    DEPENDS ${CMAKE_BINARY_DIR}/empty_allsyms.c
    COMMENT "Creating initial empty symbol table"
)

# 设置源文件属性
target_sources(${PROJECT_NAME} PRIVATE ${CMAKE_BINARY_DIR}/allsyms.c)
set_property(TARGET ${PROJECT_NAME} APPEND PROPERTY ADDITIONAL_CLEAN_FILES ${CMAKE_BINARY_DIR}/allsyms.c)
set_source_files_properties(${CMAKE_BINARY_DIR}/allsyms.c PROPERTIES GENERATED TRUE)

# 创建初始符号表目标
add_custom_target(allsyms DEPENDS ${CMAKE_BINARY_DIR}/allsyms.c)

# 确保主项目依赖于初始符号表
add_dependencies(${PROJECT_NAME} allsyms)

# 阶段2: 主项目编译后，生成第一次符号表
add_custom_target(allsyms_1
    COMMAND python3 ${BUILD_ENV_PATH}/tools/mkallsyms.py ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.elf ${CMAKE_BINARY_DIR}/allsyms_1.c --orderbyname ${CONFIG_SYMTAB_ORDEREDBYNAME}
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_BINARY_DIR}/allsyms_1.c ${CMAKE_BINARY_DIR}/allsyms.c
    DEPENDS ${PROJECT_NAME}
    COMMENT "Generating first symbol table"
)
set_property(TARGET allsyms_1 APPEND PROPERTY ADDITIONAL_CLEAN_FILES ${CMAKE_BINARY_DIR}/allsyms_1.c)

# 阶段3: 用第一次符号表重新编译
add_custom_target(rebuild_1
    COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR} --target ${PROJECT_NAME} -- -j1
    DEPENDS allsyms_1
    COMMENT "Rebuilding with first symbol table"
)

# 阶段4: 用包含符号表自身符号的ELF生成第二次符号表
add_custom_target(allsyms_2
    COMMAND python3 ${BUILD_ENV_PATH}/tools/mkallsyms.py ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.elf ${CMAKE_BINARY_DIR}/allsyms_2.c --orderbyname ${CONFIG_SYMTAB_ORDEREDBYNAME}
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_BINARY_DIR}/allsyms_2.c ${CMAKE_BINARY_DIR}/allsyms.c
    DEPENDS rebuild_1
    COMMENT "Generating final symbol table with symbols from symbol table itself"
)
set_property(TARGET allsyms_2 APPEND PROPERTY ADDITIONAL_CLEAN_FILES ${CMAKE_BINARY_DIR}/allsyms_2.c)

# 阶段5: 最终编译
add_custom_target(allsyms_rebuild
    COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR} --target ${PROJECT_NAME} -- -j1
    DEPENDS allsyms_2
    COMMENT "Final rebuilding with complete symbol table"
)

endif(CONFIG_ALLSYMS)


if(CONFIG_MM_KASAN_GLOBAL)

add_custom_target(kasan-global COMMAND python3 ${BUILD_ENV_PATH}/tools/kasan_global.py -e ${PROJECT_NAME}.elf -o ${CMAKE_BINARY_DIR}/kasan_globals.c)

target_sources(${PROJECT_NAME} PRIVATE ${CMAKE_BINARY_DIR}/kasan_globals.c)

add_dependencies(${PROJECT_NAME} kasan-global)

set_property(TARGET ${PROJECT_NAME} APPEND PROPERTY ADDITIONAL_CLEAN_FILES ${CMAKE_BINARY_DIR}/kasan_globals.c)

set_source_files_properties(${CMAKE_BINARY_DIR}/kasan_globals.c PROPERTIES GENERATED TRUE)

set_source_files_properties(${CMAKE_SOURCE_DIR}/kernel/memory/kasan/kasan.c PROPERTIES COMPILE_FLAGS "-fno-sanitize=kernel-address -fno-lto")
set_source_files_properties(${CMAKE_SOURCE_DIR}/arch/arm/earlymmu.c PROPERTIES COMPILE_FLAGS "-fno-sanitize=kernel-address -fno-lto")
set_source_files_properties(${CMAKE_SOURCE_DIR}/arch/aarch64/early_mmu.c PROPERTIES COMPILE_FLAGS "-fno-sanitize=kernel-address -fno-lto")
set_source_files_properties(${CMAKE_SOURCE_DIR}/arch/x86_64/early_mmu.c PROPERTIES COMPILE_FLAGS "-fno-sanitize=kernel-address -fno-lto")
set_source_files_properties(${CMAKE_BINARY_DIR}/kasan_globals.c PROPERTIES COMPILE_FLAGS "-fno-sanitize=kernel-address -fno-lto")
set_source_files_properties(${CMAKE_SOURCE_DIR}/kernel/memory/tlsf/tlsf.c PROPERTIES COMPILE_FLAGS "-fno-sanitize=kernel-address -fno-lto")
set_source_files_properties(${CMAKE_SOURCE_DIR}/kernel/memory/workspace/ttosHeap.c PROPERTIES COMPILE_FLAGS "-fno-sanitize=kernel-address -fno-lto")
set_source_files_properties(${CMAKE_SOURCE_DIR}/kernel/memory/page.c PROPERTIES COMPILE_FLAGS "-fno-sanitize=kernel-address -fno-lto")
set_source_files_properties(${CMAKE_SOURCE_DIR}/arch/arm/exception.c PROPERTIES COMPILE_FLAGS "-fno-sanitize=kernel-address -fno-lto")

add_custom_target(kasan-1 COMMAND cmake --build ${CMAKE_BINARY_DIR} --target=${PROJECT_NAME})
add_custom_target(kasan-2 COMMAND cmake --build ${CMAKE_BINARY_DIR} --target=${PROJECT_NAME})
add_dependencies(kasan-2 kasan-1)

endif(CONFIG_MM_KASAN_GLOBAL)

set_property(TARGET ${PROJECT_NAME} APPEND PROPERTY ADDITIONAL_CLEAN_FILES ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.map)
add_library(gcc STATIC IMPORTED GLOBAL)

set_target_properties(gcc PROPERTIES IMPORTED_LOCATION ${CMAKE_SOURCE_DIR}/arch/${CONFIG_ARCH}/libgcc.a)

add_library(k STATIC IMPORTED GLOBAL)

set_target_properties(k PROPERTIES IMPORTED_LOCATION ${CMAKE_BINARY_DIR}/libk/libs/libk.a)

target_link_libraries(${PROJECT_NAME} PRIVATE -Wl,--start-group gcc k -Wl,--end-group)

add_dependencies(${PROJECT_NAME} lds)

add_custom_target(asm_offset DEPENDS ${ASM_OFFSET_DEST_PATH}/asm-offsets.h)
add_dependencies(${PROJECT_NAME} asm_offset)
#添加make clean时要删除的文件
set_property(TARGET asm_offset APPEND PROPERTY ADDITIONAL_CLEAN_FILES  ${ASM_OFFSET_DEST_PATH}/asm-offsets.c)
set_property(TARGET asm_offset APPEND PROPERTY ADDITIONAL_CLEAN_FILES  ${ASM_OFFSET_DEST_PATH}/asm-offsets.h)
set_property(TARGET asm_offset APPEND PROPERTY ADDITIONAL_CLEAN_FILES  ${ASM_OFFSET_DEST_PATH}/asm-offsets.S)

# Add SDK target to export headers
set(SDK_OUTPUT_DIR ${CMAKE_BINARY_DIR}/sdk)

get_filename_component(MODULE_CMAKE_C_COMPILER ${CMAKE_C_COMPILER} NAME)
get_filename_component(MODULE_CMAKE_ASM_COMPILER ${CMAKE_ASM_COMPILER} NAME)
get_filename_component(MODULE_CMAKE_LD ${CMAKE_LD} NAME)
# Configure module cmake template
configure_file(
    ${BUILD_ENV_PATH}/cmake/module.cmake.in
    ${CMAKE_BINARY_DIR}/module.cmake.configured
    @ONLY
)

add_custom_target(generate_desc
  COMMAND ${CMAKE_COMMAND} -E remove_directory ${SDK_OUTPUT_DIR}
  COMMAND ${CMAKE_COMMAND} -E make_directory ${SDK_OUTPUT_DIR}
  COMMAND cp -f ${GLOBA_DESC_TEMPLATE} ${CMAKE_BINARY_DIR}/desc.c
  COMMAND ${CMAKE_C_COMPILER} -include ${CMAKE_SOURCE_DIR}/include/version.h -include ${CONFIG_C_HEADER} -E ${CMAKE_BINARY_DIR}/desc.c -o ${SDK_OUTPUT_DIR}/.desc
  #删除以#开头的行
  COMMAND sed -i "/^#/d" ${SDK_OUTPUT_DIR}/.desc
  BYPRODUCTS ${SDK_OUTPUT_DIR}/.desc
)

add_custom_target(sdk
    # Create directories
    COMMAND ${CMAKE_COMMAND} -E make_directory ${SDK_OUTPUT_DIR}/include
    COMMAND ${CMAKE_COMMAND} -E make_directory ${SDK_OUTPUT_DIR}/cmake
    COMMAND ${CMAKE_COMMAND} -E make_directory ${SDK_OUTPUT_DIR}/template
    COMMAND ${CMAKE_COMMAND} -E make_directory ${SDK_OUTPUT_DIR}/linkscript
    # Copy headers
    COMMAND ${CMAKE_COMMAND} -E copy_directory ${LIBC_INC_PATH} ${SDK_OUTPUT_DIR}/include/libk
    COMMAND ${CMAKE_COMMAND} -E copy_directory ${LWIP_INC_PATH} ${SDK_OUTPUT_DIR}/include/lwip
    COMMAND ${CMAKE_COMMAND} -E copy_directory ${KERNEL_INC_PATH} ${SDK_OUTPUT_DIR}/include/kernel
    COMMAND ${CMAKE_COMMAND} -E copy_directory ${CMAKE_SOURCE_DIR}/include/linux-comp ${SDK_OUTPUT_DIR}/include/linux-comp
    COMMAND ${CMAKE_COMMAND} -E copy_directory ${CMAKE_BINARY_DIR}/kconfig ${SDK_OUTPUT_DIR}/include/kconfig
    # Copy module build system
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_BINARY_DIR}/module.cmake.configured ${SDK_OUTPUT_DIR}/cmake/module.cmake
    COMMAND ${CMAKE_COMMAND} -E copy ${BUILD_ENV_PATH}/cmake/loadDOTCONFIG.cmake ${SDK_OUTPUT_DIR}/cmake/
    COMMAND ${CMAKE_COMMAND} -E copy ${BUILD_ENV_PATH}/cmake/tools.cmake ${SDK_OUTPUT_DIR}/cmake/
    # Copy template and documentation
    COMMAND ${CMAKE_COMMAND} -E copy_directory ${BUILD_ENV_PATH}/cmake/module_template ${SDK_OUTPUT_DIR}/template
    COMMAND ${CMAKE_COMMAND} -E copy ${BUILD_ENV_PATH}/cmake/module_template/README.md ${SDK_OUTPUT_DIR}/README.md
    # Copy kernel config files
    COMMAND ${CMAKE_COMMAND} -E copy ${CONFIG_C_HEADER} ${SDK_OUTPUT_DIR}/include/kconfig/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_BINARY_DIR}/.config ${SDK_OUTPUT_DIR}/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_SOURCE_DIR}/components/module/module.lds ${SDK_OUTPUT_DIR}/linkscript/
    COMMENT "Exporting SDK headers, build system, template and documentation to ${SDK_OUTPUT_DIR}"
)

add_dependencies(sdk generate_desc)

add_custom_target(format find ${CMAKE_SOURCE_DIR} -regex "'.*\\.\\(cpp\\|hpp\\|c\\|h\\|cc\\|cxx\\)$$'" | xargs clang-format -i )
add_custom_target(System.map ALL  ${CMAKE_NM} ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.elf > System.map)
set_property(TARGET System.map APPEND PROPERTY ADDITIONAL_CLEAN_FILES ${CMAKE_BINARY_DIR}/System.map)

add_custom_target(${PROJECT_NAME}.bin ALL
  COMMAND ${CMAKE_OBJCOPY} -O binary --strip-unneeded -R .note -R .comment -R .stab -R .stabstr ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.elf ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.bin
  COMMENT "Generating ${PROJECT_NAME}.bin"
)

add_dependencies(System.map ${PROJECT_NAME})
if(CONFIG_MM_KASAN_GLOBAL)
add_dependencies(${PROJECT_NAME}.bin kasan-2)
else(CONFIG_MM_KASAN_GLOBAL)
add_dependencies(${PROJECT_NAME}.bin ${PROJECT_NAME})
if(CONFIG_ALLSYMS)
add_dependencies(${PROJECT_NAME}.bin allsyms_rebuild)
endif(CONFIG_ALLSYMS)
endif(CONFIG_MM_KASAN_GLOBAL)
set_property(TARGET ${PROJECT_NAME}.bin APPEND PROPERTY ADDITIONAL_CLEAN_FILES ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.bin)

add_custom_target(scan rm -fr ${CMAKE_BINARY_DIR}/scan_result && analyze-build --analyzer-target ${CC_TARGET} --cdb ${CMAKE_BINARY_DIR}/compile_commands.json -o ${CMAKE_BINARY_DIR}/scan_result)
set_property(TARGET scan APPEND PROPERTY ADDITIONAL_CLEAN_FILES ${CMAKE_BINARY_DIR}/scan_result)

if(CONFIG_FIRM_VERIFY)
add_custom_command(
    TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND python3 ${TOOLS_ROOT}/firmware_md5.py
            --toolchain-prefix ${TOOLCHAIN_PREFIX}
            --input ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.elf
    COMMENT "Final write md5 with complete"
    DEPENDS CONFIG_FIRM_VERIFY
    VERBATIM
)
endif(CONFIG_FIRM_VERIFY)

add_dependencies(scan ${PROJECT_NAME})
if(CONFIG_BOARD_PATH)
    include(${CMAKE_SOURCE_DIR}/boards/${CONFIG_BOARD_PATH}/board.cmake)
    # 使用 USES_TERMINAL 标志确保 ninja 能够正确处理终端 I/O
    add_custom_target(run
        COMMAND ${BOARD_RUNCMD} ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.bin
        USES_TERMINAL
        COMMENT "Running QEMU with ${PROJECT_NAME}.bin"
    )
    add_dependencies(run ${PROJECT_NAME}.bin)

    add_custom_target(debug
        COMMAND ${BOARD_DBGCMD} ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.bin
        USES_TERMINAL
        COMMENT "Debugging with QEMU"
    )
    add_dependencies(debug ${PROJECT_NAME}.bin)

    add_custom_target(img
        DEPENDS System.map
        COMMAND ${BOARD_MKIMG} ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.bin ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.img
        COMMENT "Creating image file"
    )
    add_dependencies(img ${PROJECT_NAME}.bin)
    add_dependencies(img System.map)
endif()

# 处理内核模块构建
message(STATUS "Checking for kernel modules...")
message(STATUS "KERNEL_MODULES_LIST: ${KERNEL_MODULES_LIST}")
message(STATUS "KERNEL_MODULES_CONFIG_LIST: ${KERNEL_MODULES_CONFIG_LIST}")

# 无论是否有模块，都创建 modules 目标，避免 make modules 失败
set(ALL_KERNEL_MODULES "")

if(KERNEL_MODULES_LIST)
    message(STATUS "Found kernel modules to build:")
    list(LENGTH KERNEL_MODULES_LIST module_count)
    math(EXPR module_count_minus_1 "${module_count} - 1")

    foreach(i RANGE ${module_count_minus_1})
        list(GET KERNEL_MODULES_LIST ${i} module_path)
        list(GET KERNEL_MODULES_CONFIG_LIST ${i} config_name)

        get_filename_component(module_name ${module_path} NAME)
        message(STATUS "  - ${module_name} (${config_name})")

        # 创建模块构建目录
        set(MODULE_BUILD_DIR ${CMAKE_BINARY_DIR}/modules/${module_name})
        file(MAKE_DIRECTORY ${MODULE_BUILD_DIR})

        # 设置模板变量
        set(module_name ${module_name})
        set(module_path ${module_path})
        set(config_name ${config_name})
        set(SDK_DIR ${CMAKE_BINARY_DIR})

        # 配置模块构建脚本
        configure_file(
            ${BUILD_ENV_PATH}/cmake/module_template.cmake.in
            ${MODULE_BUILD_DIR}/CMakeLists.txt
            @ONLY
        )

        # 添加模块构建目标
        add_custom_target(${module_name}_module
            COMMAND ${CMAKE_COMMAND}
                -DSDK_DIR=${CMAKE_BINARY_DIR}
                -DMODULE_SOURCE_DIR=${module_path}
                -DMODULE_NAME=${module_name}
                -DCONFIG_NAME=${config_name}
                -S ${MODULE_BUILD_DIR}
                -B ${MODULE_BUILD_DIR}/build
            COMMAND ${CMAKE_COMMAND} --build ${MODULE_BUILD_DIR}/build
            WORKING_DIRECTORY ${MODULE_BUILD_DIR}
            COMMENT "Building kernel module: ${module_name}"
            DEPENDS ${PROJECT_NAME}
        )

        list(APPEND ALL_KERNEL_MODULES ${module_name}_module)
    endforeach()

    message(STATUS "Use 'make modules' to build all kernel modules")
else()
    message(STATUS "No kernel modules configured for separate building")
endif()

# 总是创建 modules 目标
add_custom_target(modules
    DEPENDS ${ALL_KERNEL_MODULES}
    COMMENT "Building all kernel modules"
)
