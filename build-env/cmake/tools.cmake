
# 2.2 Misc
#
# import_kconfig(<prefix> <kconfig_fragment> [<keys>])
#
# Parse a KConfig fragment (typically with extension .config) and
# introduce all the symbols that are prefixed with 'prefix' into the
# CMake namespace. List all created variable names in the 'keys'
# output variable if present.
function(import_kconfig prefix kconfig_fragment)
  # Parse the lines prefixed with 'prefix' in ${kconfig_fragment}
  file(
    STRINGS
    ${kconfig_fragment}
    DOT_CONFIG_LIST
    REGEX "^${prefix}"
    ENCODING "UTF-8"
  )

  foreach (CONFIG ${DOT_CONFIG_LIST})

    # CONFIG could look like: CONFIG_NET_BUF=y

    # Match the first part, the variable name
    string(REGEX MATCH "[^=]+" CONF_VARIABLE_NAME ${CONFIG})

    # Match the second part, variable value
    string(REGEX MATCH "=(.+$)" CONF_VARIABLE_VALUE ${CONFIG})
    # The variable name match we just did included the '=' symbol. To just get the
    # part on the RHS we use match group 1
    set(CONF_VARIABLE_VALUE ${CMAKE_MATCH_1})

    if("${CONF_VARIABLE_VALUE}" MATCHES "^\"(.*)\"$") # Is surrounded by quotes
      set(CONF_VARIABLE_VALUE ${CMAKE_MATCH_1})
    endif()

    set("${CONF_VARIABLE_NAME}" "${CONF_VARIABLE_VALUE}" PARENT_SCOPE)
    list(APPEND keys "${CONF_VARIABLE_NAME}")
  endforeach()

  foreach(outvar ${ARGN})
    set(${outvar} "${keys}" PARENT_SCOPE)
  endforeach()
endfunction()


# 2.2 Misc
#
# SUBDIRLIST(<var>)
#
macro(SUBDIRLIST result)
    string(REGEX REPLACE "/$" "" CURRENT_FOLDER_ABSOLUTE ${CMAKE_CURRENT_SOURCE_DIR})
    file(GLOB children RELATIVE ${CURRENT_FOLDER_ABSOLUTE} ${CURRENT_FOLDER_ABSOLUTE}/*)
    set(dirlist "")
    foreach(child ${children})
        if(IS_DIRECTORY ${CURRENT_FOLDER_ABSOLUTE}/${child})
            LIST(APPEND dirlist ${child})
        endif()
    endforeach()
    set(${result} ${dirlist})
endmacro()

# 2.2 Misc
#
# ADD_SUBDIR(<subdir>)
#
macro(REMOVE_SRC list filename)
    foreach(file ${${list}})
      if( "${file}" MATCHES "${filename}$")
          list(REMOVE_ITEM ${list} ${file})
      endif()
    endforeach()
endmacro()

macro(REMOVE_SRC_IF config list filename)
if(${config})
    foreach(file ${${list}})
      if( "${file}" MATCHES "${filename}$")
          list(REMOVE_ITEM ${list} ${file})
      endif()
    endforeach()
endif()
endmacro()

macro(REMOVE_SRC_IFNOT config list filename)
if(NOT ${config})
    foreach(file ${${list}})
      if( "${file}" MATCHES "${filename}$")
          list(REMOVE_ITEM ${list} ${file})
      endif()
    endforeach()
endif()
endmacro()

macro(ADD_SRC_IF config list filename)
if(${config})
    list(APPEND ${list} ${CMAKE_CURRENT_SOURCE_DIR}/${filename})
endif()
endmacro()

macro(ADD_SUBDIR subdir)
    string(REGEX REPLACE "/$" "" CURRENT_FOLDER_ABSOLUTE ${CMAKE_CURRENT_SOURCE_DIR})
    if(EXISTS ${CURRENT_FOLDER_ABSOLUTE}/${subdir}/CMakeLists.txt)
        add_subdirectory(${subdir})
    endif()
endmacro()

macro(ADD_SUBDIR_IF config subdir)
    if(${config})
      string(REGEX REPLACE "/$" "" CURRENT_FOLDER_ABSOLUTE ${CMAKE_CURRENT_SOURCE_DIR})
      if(EXISTS ${CURRENT_FOLDER_ABSOLUTE}/${subdir}/CMakeLists.txt)
          add_subdirectory(${subdir})
      endif()
    endif()
endmacro()

macro(ADD_SUBDIR_IFNOT config subdir)
    if(NOT ${config})
      string(REGEX REPLACE "/$" "" CURRENT_FOLDER_ABSOLUTE ${CMAKE_CURRENT_SOURCE_DIR})
      if(EXISTS ${CURRENT_FOLDER_ABSOLUTE}/${subdir}/CMakeLists.txt)
          add_subdirectory(${subdir})
      endif()
    endif()
endmacro()

macro(APPLY_LINKER_SCRIPT filename)
    add_custom_target(lds cp ${filename} ${CMAKE_BINARY_DIR}/link.c && ${CMAKE_C_COMPILER} -include ${CONFIG_C_HEADER} -E -P ${CMAKE_BINARY_DIR}/link.c -o ${CMAKE_BINARY_DIR}/link.lds &&
    sed -i "s/^#.*//g" ${CMAKE_BINARY_DIR}/link.lds &&
    rm -f ${CMAKE_BINARY_DIR}/link.c)
    set_property(TARGET lds APPEND PROPERTY ADDITIONAL_CLEAN_FILES ${CMAKE_BINARY_DIR}/link.lds)
endmacro()

# 检查配置是否为模块 (m)
macro(CONFIG_IS_MODULE config_name result_var)
    if(DEFINED ${config_name})
        if("${${config_name}}" STREQUAL "m")
            set(${result_var} TRUE)
        else()
            set(${result_var} FALSE)
        endif()
    else()
        set(${result_var} FALSE)
    endif()
endmacro()

# 检查配置是否启用 (y 或 m)
macro(CONFIG_IS_ENABLED config_name result_var)
    if(DEFINED ${config_name})
        if("${${config_name}}" STREQUAL "y" OR "${${config_name}}" STREQUAL "m")
            set(${result_var} TRUE)
        else()
            set(${result_var} FALSE)
        endif()
    else()
        set(${result_var} FALSE)
    endif()
endmacro()

# 条件添加子目录 - 支持模块化
macro(ADD_SUBDIR_IF_ENABLED config subdir)
    CONFIG_IS_ENABLED(${config} _config_enabled)
    if(_config_enabled)
        CONFIG_IS_MODULE(${config} _is_module)
        if(_is_module)
            # 如果是模块，记录到模块列表中，稍后单独构建
            list(APPEND KERNEL_MODULES_LIST "${CMAKE_CURRENT_SOURCE_DIR}/${subdir}")
            list(APPEND KERNEL_MODULES_CONFIG_LIST "${config}")
            set(KERNEL_MODULES_LIST ${KERNEL_MODULES_LIST} PARENT_SCOPE)
            set(KERNEL_MODULES_CONFIG_LIST ${KERNEL_MODULES_CONFIG_LIST} PARENT_SCOPE)
        else()
            # 如果是内建，正常添加到内核
            string(REGEX REPLACE "/$" "" CURRENT_FOLDER_ABSOLUTE ${CMAKE_CURRENT_SOURCE_DIR})
            if(EXISTS ${CURRENT_FOLDER_ABSOLUTE}/${subdir}/CMakeLists.txt)
                add_subdirectory(${subdir})
            endif()
        endif()
    endif()
endmacro()

# 条件添加源文件 - 支持模块化
macro(ADD_SRC_IF_ENABLED config list filename)
    CONFIG_IS_ENABLED(${config} _config_enabled)
    if(_config_enabled)
        CONFIG_IS_MODULE(${config} _is_module)
        if(NOT _is_module)
            # 只有内建时才添加到内核源文件列表
            list(APPEND ${list} ${CMAKE_CURRENT_SOURCE_DIR}/${filename})
        endif()
    endif()
endmacro()

# 为模块创建独立的构建目标
function(CREATE_MODULE_TARGET module_path config_name)
    get_filename_component(module_name ${module_path} NAME)

    # 创建模块构建脚本
    set(MODULE_BUILD_DIR ${CMAKE_BINARY_DIR}/modules/${module_name})
    file(MAKE_DIRECTORY ${MODULE_BUILD_DIR})

    # 生成模块专用的 CMakeLists.txt
    configure_file(
        ${BUILD_ENV_PATH}/cmake/module_template.cmake.in
        ${MODULE_BUILD_DIR}/CMakeLists.txt
        @ONLY
    )

    # 添加模块构建目标
    add_custom_target(${module_name}_module
        COMMAND ${CMAKE_COMMAND} -S ${MODULE_BUILD_DIR} -B ${MODULE_BUILD_DIR}/build
        COMMAND ${CMAKE_COMMAND} --build ${MODULE_BUILD_DIR}/build
        WORKING_DIRECTORY ${MODULE_BUILD_DIR}
        COMMENT "Building kernel module: ${module_name}"
    )

    # 添加到模块构建列表
    list(APPEND ALL_KERNEL_MODULES ${module_name}_module)
    set(ALL_KERNEL_MODULES ${ALL_KERNEL_MODULES} PARENT_SCOPE)
endfunction()
