cmake_minimum_required(VERSION 3.13)

# 从命令行获取参数
if(NOT DEFINED MODULE_NAME)
    message(FATAL_ERROR "MODULE_NAME must be defined")
endif()

if(NOT DEFINED MODULE_SOURCE_DIR)
    message(FATAL_ERROR "MODULE_SOURCE_DIR must be defined")
endif()

if(NOT DEFINED SDK_DIR)
    message(FATAL_ERROR "SDK_DIR must be defined")
endif()

if(NOT DEFINED CONFIG_NAME)
    message(FATAL_ERROR "CONFIG_NAME must be defined")
endif()

project(@module_name@_module C)

# 包含 SDK 的模块构建系统
include(@SDK_DIR@/../build-env/cmake/module.cmake)

# 收集模块源文件
file(GLOB_RECURSE MODULE_SOURCES
    @module_path@/*.c
    @module_path@/*.S
    @module_path@/*.cpp
    @module_path@/*.cxx
    @module_path@/*.c++
)

# 添加内核模块
add_kernel_module(@module_name@ ${MODULE_SOURCES})

# 设置模块特定的编译定义
target_compile_definitions(@module_name@_obj PRIVATE
    "@config_name@=1"
    "@config_name@_MODULE=1"
)

# 添加模块特定的包含路径
target_include_directories(@module_name@_obj PRIVATE
    @module_path@
    @SDK_DIR@/include
)
