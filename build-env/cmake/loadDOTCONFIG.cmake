if(NOT EXISTS ${DOTCONFIG})
    file(TOUCH ${DOTCONFIG})
endif()

# Support assigning Kconfig symbols on the command-line with CMake
# cache variables prefixed with 'CONFIG_'. This feature is
# experimental and undocumented until it has undergone more
# user-testing.
unset(EXTRA_KCONFIG_OPTIONS)
get_cmake_property(cache_variable_names CACHE_VARIABLES)
foreach (name ${cache_variable_names})
    # message(${cache_variable_names})
  if("${name}" MATCHES "^CONFIG_")
    # message(${name})
    # When a cache variable starts with 'CONFIG_', it is assumed to be
    # a Kconfig symbol assignment from the CMake command line.
    set(EXTRA_KCONFIG_OPTIONS
      "${EXTRA_KCONFIG_OPTIONS}\n${name}=${${name}}"
      )
  endif()
endforeach()
# Remove the CLI Kconfig symbols from the namespace and
# CMakeCache.txt. If the symbols end up in DOTCONFIG they will be
# re-introduced to the namespace through 'import_kconfig'.
foreach (name ${cache_variable_names})
  if("${name}" MATCHES "^CONFIG_")
    unset(${name})
    unset(${name} CACHE)
  endif()
endforeach()

# Parse the lines prefixed with CONFIG_ in the .config file from Kconfig
import_kconfig(CONFIG_ ${DOTCONFIG})

# 初始化模块列表
set(KERNEL_MODULES_LIST "")
set(KERNEL_MODULES_CONFIG_LIST "")
set(ALL_KERNEL_MODULES "")

# Re-introduce the CLI Kconfig symbols that survived
foreach (name ${cache_variable_names})
  if("${name}" MATCHES "^CONFIG_")
    if(DEFINED ${name})
      set(${name} ${${name}} CACHE STRING "")
      message(${${name}})
    endif()
  endif()
endforeach()

# 处理模块配置，为 'm' 状态的配置生成相应的宏定义
get_cmake_property(all_vars VARIABLES)
foreach(var_name ${all_vars})
  if("${var_name}" MATCHES "^CONFIG_")
    if(DEFINED ${var_name} AND "${${var_name}}" STREQUAL "m")
      # 为模块状态的配置添加 _MODULE 后缀的宏定义
      set(${var_name}_MODULE "1")
      message(STATUS "Module configuration detected: ${var_name}=m")
    endif()
  endif()
endforeach()
