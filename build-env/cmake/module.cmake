cmake_minimum_required(VERSION 3.13)

# Include required cmake modules
include(${SDK_DIR}/../build-env/cmake/tools.cmake)

# Set DOTCONFIG path to use the SDK's .config file
set(DOTCONFIG ${SDK_DIR}/.config)

# Load kernel configuration
include(${SDK_DIR}/../build-env/cmake/loadDOTCONFIG.cmake)

# Set compiler and flags to match kernel compilation
# 从 SDK 目录读取编译器配置
if(EXISTS ${SDK_DIR}/cmake_cache.txt)
    file(STRINGS ${SDK_DIR}/cmake_cache.txt CMAKE_CACHE_CONTENT)
    foreach(line ${CMAKE_CACHE_CONTENT})
        if(line MATCHES "CMAKE_C_COMPILER:FILEPATH=(.+)")
            set(CMAKE_C_COMPILER ${CMAKE_MATCH_1})
        elseif(line MATCHES "CMAKE_ASM_COMPILER:FILEPATH=(.+)")
            set(CMAKE_ASM_COMPILER ${CMAKE_MATCH_1})
        elseif(line MATCHES "CMAKE_LD:FILEPATH=(.+)")
            set(CMAKE_LD ${CMAKE_MATCH_1})
        endif()
    endforeach()
endif()

# 如果无法从缓存读取，使用默认值
if(NOT CMAKE_C_COMPILER)
    find_program(CMAKE_C_COMPILER gcc)
endif()
if(NOT CMAKE_ASM_COMPILER)
    set(CMAKE_ASM_COMPILER ${CMAKE_C_COMPILER})
endif()
if(NOT CMAKE_LD)
    find_program(CMAKE_LD ld)
endif()

# Set compilation flags
set(CMAKE_C_FLAGS "-fno-builtin -nostdinc -fno-pic -fno-pie")
set(CMAKE_ASM_FLAGS "")

# Include paths
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${SDK_DIR}/include/kernel
    ${SDK_DIR}/include/libk
    ${SDK_DIR}/include/lwip
    ${SDK_DIR}/include/linux-comp
    ${SDK_DIR}/kconfig/include/generated
)

# Add kernel config header to compilation
add_definitions(-include ${SDK_DIR}/kconfig/include/generated/autoconfig.h)

if(CONFIG_ARCH_ARMv7 OR CONFIG_ARCH_AARCH32)
include_directories(
    ${SDK_DIR}/include/kernel/arch/arm
    ${SDK_DIR}/include/kernel/arch/arm_common
)
endif(CONFIG_ARCH_ARMv7 OR CONFIG_ARCH_AARCH32)

if(CONFIG_ARCH_AARCH64)
include_directories(
        ${SDK_DIR}/include/kernel/arch/aarch64
        ${SDK_DIR}/include/kernel/arch/arm_common
)
endif(CONFIG_ARCH_AARCH64)

# Function to create kernel module target
function(add_kernel_module MODULE_NAME)
    add_library(${MODULE_NAME}_obj OBJECT ${ARGN})

    # 查找模块链接脚本
    set(MODULE_LDS_PATH "")
    if(EXISTS ${SDK_DIR}/../components/module/module.lds)
        set(MODULE_LDS_PATH ${SDK_DIR}/../components/module/module.lds)
    elseif(EXISTS ${SDK_DIR}/linkscript/module.lds)
        set(MODULE_LDS_PATH ${SDK_DIR}/linkscript/module.lds)
    else()
        message(WARNING "Module linker script not found, using default linking")
    endif()

    if(MODULE_LDS_PATH)
        add_custom_command(
            OUTPUT ${MODULE_NAME}.ko
            COMMAND ${CMAKE_LD} -r -T ${MODULE_LDS_PATH} $<TARGET_OBJECTS:${MODULE_NAME}_obj> -o ${MODULE_NAME}.ko
            COMMAND_EXPAND_LISTS
            DEPENDS ${MODULE_NAME}_obj
            COMMENT "Linking kernel module ${MODULE_NAME}"
        )
    else()
        add_custom_command(
            OUTPUT ${MODULE_NAME}.ko
            COMMAND ${CMAKE_LD} -r $<TARGET_OBJECTS:${MODULE_NAME}_obj> -o ${MODULE_NAME}.ko
            COMMAND_EXPAND_LISTS
            DEPENDS ${MODULE_NAME}_obj
            COMMENT "Linking kernel module ${MODULE_NAME}"
        )
    endif()
    
    add_custom_target(${MODULE_NAME} ALL DEPENDS ${MODULE_NAME}.ko)
    target_compile_definitions(${MODULE_NAME}_obj PRIVATE
        MODULE
        __KERNEL__
        KBUILD_MODNAME=\"${MODULE_NAME}\"
    )
endfunction()
