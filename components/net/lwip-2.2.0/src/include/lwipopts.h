
#ifndef __LWIP_OPTS_SYS__
#define __LWIP_OPTS_SYS__

#include <system/kconfig.h>

#define NO_SYS                          0

/* 外部配置 */

/**
 * LWIP_IPV4==1: Enable IPv4
 */
#define LWIP_IPV4                       (IS_ENABLED(CONFIG_LWIP_IPV4))

/**
 * LWIP_IPV6==1: Enable IPv6
 */
/* 不支持 */
#define LWIP_IPV6                       0

/**
 * IP_FORWARD==1: Enables the ability to forward IP packets across network
 * interfaces. If you are going to run lwIP on a device with only one network
 * interface, define this to 0.
 */
#define IP_FORWARD                      (IS_ENABLED(CONFIG_LWIP_IPFORWARD))

/**
 * LWIP_TCP==1: Turn on TCP.
 */
#define LWIP_TCP                        (IS_ENABLED(CONFIG_LWIP_TCP))

/**
 * LWIP_UDP==1: Turn on UDP.
 */
#define LWIP_UDP                        (IS_ENABLED(CONFIG_LWIP_UDP))

/**
 * LWIP_RAW==1: Enable application layer to hook into the IP layer itself.
 */
#define LWIP_RAW                        (IS_ENABLED(CONFIG_LWIP_RAW))

/**
 * LWIP_ARP==1: Enable ARP functionality.
 */
#define LWIP_ARP                        (IS_ENABLED(CONFIG_LWIP_ARP))

/**
 * LWIP_IGMP==1: Turn on IGMP module.
 */
#define LWIP_IGMP                       (IS_ENABLED(CONFIG_LWIP_IGMP))

/**
 * LWIP_IGMP==1: Turn on DHCP.
 */
#define LWIP_DHCP                       (IS_ENABLED(CONFIG_LWIP_DHCP))

/**
 * LWIP_IGMP==1: Turn on AutoIP.
 */
#define LWIP_AUTOIP                     (IS_ENABLED(CONFIG_LWIP_AUTOIP))

/**
 * LWIP_DNS==1: Turn on DNS module. UDP must be available for DNS
 * transport.
 */
#define LWIP_DNS                        (IS_ENABLED(CONFIG_LWIP_DNS))

/**
 * LWIP_TCPIP_CORE_LOCKING
 * Creates a global mutex that is held during TCPIP thread operations.
 * Can be locked by client code to perform lwIP operations without changing
 * into TCPIP thread using callbacks. See LOCK_TCPIP_CORE() and
 * UNLOCK_TCPIP_CORE().
 * Your system should provide mutexes supporting priority inversion to use this.
 */
#define LWIP_TCPIP_CORE_LOCKING         (IS_ENABLED(CONFIG_LWIP_CORE_LOCKING))

/**
 * LWIP_TCPIP_CORE_LOCKING_INPUT: when LWIP_TCPIP_CORE_LOCKING is enabled,
 * this lets tcpip_input() grab the mutex for input packets as well,
 * instead of allocating a message and passing it to tcpip_thread.
 *
 * ATTENTION: this does not work when tcpip_input() is called from
 * interrupt context!
 */
#define LWIP_TCPIP_CORE_LOCKING_INPUT   (IS_ENABLED(CONFIG_LWIP_CORE_LOCKING_INPUT))

/**
 * TCPIP_THREAD_PRIO: The priority assigned to the main tcpip thread.
 * The priority value itself is platform-dependent, but is passed to
 * sys_thread_new() when the thread is created.
 */
#define TCPIP_THREAD_PRIO               (CONFIG_LWIP_TCPIP_THREAD_PRIO)

/**
 * TCPIP_THREAD_STACKSIZE: The stack size used by the main tcpip thread.
 * The stack size value itself is platform-dependent, but is passed to
 * sys_thread_new() when the thread is created.
 */
#define TCPIP_THREAD_STACKSIZE          (CONFIG_LWIP_TCPIP_THREAD_STACK)

/**
 * TCPIP_MBOX_SIZE: The mailbox size for the tcpip thread messages
 * The queue size value itself is platform-dependent, but is passed to
 * sys_mbox_new() when tcpip_init is called.
 */
#define TCPIP_MBOX_SIZE                 (CONFIG_LWIP_TCPIP_MBOX_SIZE)

/**
 * MEMP_NUM_NETCONN: the number of struct netconns.
 * (only needed if you use the sequential API, like api_lib.c)
 */
#define MEMP_NUM_NETCONN                (CONFIG_LWIP_NETCONN_NUM)

#define DEFAULT_ACCEPTMBOX_SIZE         (CONFIG_LWIP_NETCONN_ACCEPT_MBOX_SIZE)

#define DEFAULT_TCP_RECVMBOX_SIZE       (CONFIG_LWIP_NETCONN_TCP_MBOX_SIZE)

#define DEFAULT_UDP_RECVMBOX_SIZE       (CONFIG_LWIP_NETCONN_UDP_MBOX_SIZE)

#define DEFAULT_RAW_RECVMBOX_SIZE       (CONFIG_LWIP_NETCONN_RAW_MBOX_SIZE)

/**
 * TCP_MSS: TCP Maximum segment size. (default is 536, a conservative default,
 * you might want to increase this.)
 * For the receive side, this MSS is advertised to the remote side
 * when opening a connection. For the transmit size, this MSS sets
 * an upper limit on the MSS advertised by the remote host.
 */
#define TCP_MSS                         (CONFIG_LWIP_TCP_MSS)

#define LWIP_CHECKSUM_ON_COPY           (IS_ENABLED(CONFIG_LWIP_CHECKSUM_COPY))

#define CHECKSUM_CHECK_IP               (IS_ENABLED(CONFIG_LWIP_CHECKSUM_IP))

#define CHECKSUM_CHECK_TCP              (IS_ENABLED(CONFIG_LWIP_CHECKSUM_TCP))

#define CHECKSUM_CHECK_UDP              (IS_ENABLED(CONFIG_LWIP_CHECKSUM_UDP))

#define CHECKSUM_CHECK_ICMP             (IS_ENABLED(CONFIG_LWIP_CHECKSUM_ICMP))

/**
 * LWIP_STATS==1: Enable statistics collection in lwip_stats.
 */
#define LWIP_STATS                      (IS_ENABLED(CONFIG_LWIP_INTERNAL_STATS))


/* 内部配置 */

/**
 * LWIP_COMPAT_SOCKETS==1: Enable BSD-style sockets functions names through defines.
 * LWIP_COMPAT_SOCKETS==2: Same as ==1 but correctly named functions are created.
 * While this helps code completion, it might conflict with existing libraries.
 * (only used if you use sockets.c)
 */
#define LWIP_COMPAT_SOCKETS             (!IS_ENABLED(CONFIG_LWIP_EXTERNAL_LIBC))

/* #define  LWIP_ERRNO_INCLUDE <errno.h> */
#define LWIP_ERRNO_STDINCLUDE           (IS_ENABLED(CONFIG_LWIP_EXTERNAL_LIBC))

/**
 * MEM_LIBC_MALLOC==1: Use malloc/free/realloc provided by your C-library
 * instead of the lwip internal allocator. Can save code size if you
 * already use it.
 */
#define MEM_LIBC_MALLOC                 (IS_ENABLED(CONFIG_LWIP_USE_SYS_MALLOC))

/**
 * MEMP_MEM_MALLOC==1: Use mem_malloc/mem_free instead of the lwip pool allocator.
 * Especially useful with MEM_LIBC_MALLOC but handle with care regarding execution
 * speed (heap alloc can be much slower than pool alloc) and usage from interrupts
 * (especially if your netif driver allocates PBUF_POOL pbufs for received frames
 * from interrupt)!
 * ATTENTION: Currently, this uses the heap for ALL pools (also for private pools,
 * not only for internal pools defined in memp_std.h)!
 */
#define MEMP_MEM_MALLOC                 (IS_ENABLED(CONFIG_LWIP_MEMP_BASED_ON_HEAP))

/**
 * LWIP_SOCKET_EXTERNAL_HEADERS==1: Use external headers instead of sockets.h
 * and inet.h. In this case, user must provide its own headers by setting the
 * values for LWIP_SOCKET_EXTERNAL_HEADER_SOCKETS_H and
 * LWIP_SOCKET_EXTERNAL_HEADER_INET_H to appropriate include file names and the
 * whole content of the default sockets.h and inet.h is skipped.
 */
#define LWIP_SOCKET_EXTERNAL_HEADERS    (IS_ENABLED(CONFIG_LWIP_EXTERNAL_LIBC))
#if LWIP_SOCKET_EXTERNAL_HEADERS
#define LWIP_SOCKET_EXTERNAL_HEADER_SOCKETS_H  <arch/socket_ext.h>
#define LWIP_SOCKET_EXTERNAL_HEADER_INET_H     <arpa/inet.h>
#endif

/**
 * LWIP_POSIX_SOCKETS_IO_NAMES==1: Enable POSIX-style sockets functions names.
 * Disable this option if you use a POSIX operating system that uses the same
 * names (read, write & close). (only used if you use sockets.c)
 */
#define LWIP_POSIX_SOCKETS_IO_NAMES     (!IS_ENABLED(CONFIG_LWIP_EXTERNAL_LIBC))

#define MEMCPY(dst, src, len)           memcpy((void *)dst, (const void *)src, (uint32_t)len)
#define SMEMCPY(dst, src, len)          MEMCPY(dst, src, len)
#define IPADDR_WORDALIGNED_COPY_TO_IP4_ADDR_T(dst, src)    MEMCPY(dst, src, (uint32_t)sizeof(ip4_addr_t))
#define IPADDR_WORDALIGNED_COPY_FROM_IP4_ADDR_T(dst, src)  IPADDR_WORDALIGNED_COPY_TO_IP4_ADDR_T(dst, src)

#if !LWIP_IPV6
typedef struct ip4_addr ip4_addr_t;
extern struct netif *impl_lwip_route(const ip4_addr_t *dest);
#define LWIP_HOOK_IP4_ROUTE(dst)        impl_lwip_route(dst)
#endif

#define LWIP_RAND()                     ((u32_t)rand())

/**
 * LWIP_SOCKET_OFFSET==n: Increases the file descriptor number created by LwIP with n.
 * This can be useful when there are multiple APIs which create file descriptors.
 * When they all start with a different offset and you won't make them overlap you can
 * re implement read/write/close/ioctl/fnctl to send the requested action to the right
 * library (sharing select will need more work though).
 */
/* LwIP内部socket号码偏移，外层适配系统socket号 */
#define LWIP_SOCKET_OFFSET              0

#define LWIP_NETIF_TX_SINGLE_PBUF       1

/**
 * LWIP_NETIF_API==1: Support netif api (in netifapi.c)
 */
#define LWIP_NETIF_API                  1

/**
 * LWIP_SINGLE_NETIF==1: use a single netif only. This is the common case for
 * small real-life targets. Some code like routing etc. can be left out.
 */
#define LWIP_SINGLE_NETIF               0

#define LWIP_WND_SCALE                  1

#define TCP_RCV_SCALE                   5

#define LWIP_TCP_TIMESTAMPS             1

/**
 * LWIP_SO_RCVBUF==1: Enable SO_RCVBUF processing.
 */
#define LWIP_SO_RCVBUF                  1

/**
 * LWIP_SO_SNDBUF==1: Enable LWIP_SO_SNDBUF processing.
 */
#define LWIP_SO_SNDBUF                  1

/**
 * LWIP_NETIF_LOOPBACK==1: Support sending packets with a destination IP
 * address equal to the netif IP address, looping them back up the stack.
 */
/* 协议栈支持回环，即可以向本地发送数据，协议栈将创建回环网卡 */
#define LWIP_NETIF_LOOPBACK             1

#define LWIP_LOOPIF_MULTICAST           1

/**
 * LWIP_SO_SNDTIMEO==1: Enable send timeout for sockets/netconns and
 * SO_SNDTIMEO processing.
 */
#define LWIP_SO_SNDTIMEO                1

/**
 * LWIP_SO_RCVTIMEO==1: Enable receive timeout for sockets/netconns and
 * SO_RCVTIMEO processing.
 */
/* 支持通过socket option SO_RCVTIMEO设置等待连接、接收数据超时时间 */
#define LWIP_SO_RCVTIMEO                1

/**
 * LWIP_TIMERS==0: Drop support for sys_timeout and lwip-internal cyclic timers.
 * (the array of lwip-internal cyclic timers is still provided)
 * (check NO_SYS_NO_TIMERS for compatibility to old versions)
 */
#define LWIP_TIMERS                     1

/** LWIP_TIMEVAL_PRIVATE: if you want to use the struct timeval provided
 * by your system, set this to 0 and include <sys/time.h> in cc.h
 */
/* LWIP定义的timeval与OS重定义冲突时，设为0，并在cc.h中添加包含timeval的头文件 */
#define LWIP_TIMEVAL_PRIVATE            0

/*
 * 是否使用关中断方式保护临界区域
 */
#define SYS_ARCH_PROTECT_WITH_INTERRUTP 0

/*
 * Disable TCP sanity checks
*/
#define  LWIP_DISABLE_TCP_SANITY_CHECKS 1

/**
 * MEM_ALIGNMENT: should be set to the alignment of the CPU
 *    4 byte alignment -> \#define MEM_ALIGNMENT 4
 *    2 byte alignment -> \#define MEM_ALIGNMENT 2
 */
#ifdef CONFIG_OS_LP64
    #define MEM_ALIGNMENT               8
#else
    #define MEM_ALIGNMENT               4
#endif /* CONFIG_OS_LP64 */

/**
 * MEM_SIZE: the size of the heap memory. If the application will send
 * a lot of data that needs to be copied, this should be set high.
 */
#ifdef CONFIG_LWIP_MEM_HEAP_SIZE
#define MEM_SIZE                        (CONFIG_LWIP_MEM_HEAP_SIZE * 1024 * 1024)
#else
#define MEM_SIZE                        (32 * 1024 * 1024)
#endif

/**
 * PBUF_POOL_SIZE: the number of buffers in the pbuf pool.
 */
/* MEM_PBUF_POOL池中Pbuf结构的数量，用于PBUF_POOL类型的Pbuf结构 */
#define PBUF_POOL_SIZE                  CONFIG_LWIP_PBUF_POOL_PBUF_NUM

/**
 * PBUF_POOL_BUFSIZE: the size of each pbuf in the pbuf pool. The default is
 * designed to accommodate single full size TCP frame in one pbuf, including
 * TCP_MSS, IP header, and link header.
 */
/* MEM_PBUF_POOL池中Pbuf结构的大小，即PBUF_POOL类型的Pbuf结构大小 */
#define PBUF_POOL_BUFSIZE               CONFIG_LWIP_PBUF_POOL_PBUF_BUFSIZE

/**
 * MEMP_NUM_PBUF: the number of memp struct pbufs (used for PBUF_ROM and PBUF_REF).
 * If the application sends a lot of data out of ROM (or other static memory),
 * this should be set high.
 */
/* MEM_PBUF池中Pbuf结构的数量，用于PBUF_REF和PBUF_ROM类型的Pbuf结构 */
#define MEMP_NUM_PBUF                   10240

/**
 * MEMP_NUM_NETBUF: the number of struct netbufs.
 * (only needed if you use the sequential API, like api_lib.c)
 */
#define MEMP_NUM_NETBUF                 CONFIG_LWIP_NETBUF_POOL_NETBUF_NUM

#define MEMP_NUM_SELECT_CB              1024

#define MEMP_NUM_TCPIP_MSG_API          1024

#define MEMP_NUM_TCPIP_MSG_INPKT        1024

#ifdef CONFIG_LWIP_RX_USE_PBUF_POOL
#define RX_PBUF_TYPE PBUF_POOL
#else
#define RX_PBUF_TYPE PBUF_RAM
#endif

/**
 * MEMP_NUM_UDP_PCB: the number of UDP protocol control blocks. One
 * per active UDP "connection".
 * (requires the LWIP_UDP option)
 */
#define MEMP_NUM_UDP_PCB                10240

/**
 * MEMP_NUM_TCP_PCB: the number of simultaneously active TCP connections.
 * (requires the LWIP_TCP option)
 */
#define MEMP_NUM_TCP_PCB                10240

/**
 * MEMP_NUM_TCP_PCB_LISTEN: the number of listening TCP connections.
 * (requires the LWIP_TCP option)
 */
#define MEMP_NUM_TCP_PCB_LISTEN         256

/**
 * MEMP_NUM_TCP_SEG: the number of simultaneously queued TCP segments.
 * (requires the LWIP_TCP option)
 */
#define MEMP_NUM_TCP_SEG                8192

/**
 * MEMP_NUM_REASSDATA: the number of IP packets simultaneously queued for
 * reassembly (whole packets, not fragments!)
 */
#define MEMP_NUM_REASSDATA              8192

/**
 * MEMP_NUM_ARP_QUEUE: the number of simultaneously queued outgoing
 * packets (pbufs) that are waiting for an ARP request (to resolve
 * their destination address) to finish.
 * (requires the ARP_QUEUEING option)
 */
#define MEMP_NUM_ARP_QUEUE              127

/** ETHARP_SUPPORT_STATIC_ENTRIES==1: enable code to support static ARP table
 * entries (using etharp_add_static_entry/etharp_remove_static_entry).
 */
#define ETHARP_SUPPORT_STATIC_ENTRIES   1

/**
 * IP_REASS_MAX_PBUFS: Total maximum amount of pbufs waiting to be reassembled.
 * Since the received pbufs are enqueued, be sure to configure
 * PBUF_POOL_SIZE > IP_REASS_MAX_PBUFS so that the stack is still able to receive
 * packets even if the maximum amount of fragments is enqueued for reassembly!
 * When IPv4 *and* IPv6 are enabled, this even changes to
 * (PBUF_POOL_SIZE > 2 * IP_REASS_MAX_PBUFS)!
 */
#define IP_REASS_MAX_PBUFS              10240

/**
 * IP_SOF_BROADCAST=1: Use the SOF_BROADCAST field to enable broadcast
 * filter per pcb on udp and raw send operations. To enable broadcast filter
 * on recv operations, you also have to set IP_SOF_BROADCAST_RECV=1.
 */
#define IP_SOF_BROADCAST                1

/**
 * IP_SOF_BROADCAST_RECV (requires IP_SOF_BROADCAST=1) enable the broadcast
 * filter on recv operations.
 */
#define IP_SOF_BROADCAST_RECV           1

/* 配置该项后，发送数据包时，若arp表中未查询到对方ip对应的mac，则将数据包排队挂在arp表项上，
 * 发arp广播，等收到arp响应后，再将排队的数据发送出去。
 * 若不配置该项，则只能挂一个数据包，之前的数据包则丢弃。在用户tcp通信任务和udp通信任务并存对于同一个目的ip的通信场景下，
 * 存在 在收到arp响应前，udp数据包先挂在arp表项上，然后tcp数据包又将挂在同一个arp表项上，导致先挂udp数据包被丢弃。由于tcp具有
 * 重传功能，所以，tcp不会出现数据包发不出去的情况。
*/
#define ARP_QUEUEING                    1

/* 最大支持的排队包个数 */
#define ARP_QUEUE_LEN                   64

/**
 * LWIP_BROADCAST_PING==1: respond to broadcast pings (default is unicast only)
 */
#define LWIP_BROADCAST_PING             1

/**
 * LWIP_MULTICAST_PING==1: respond to multicast pings (default is unicast only)
 */
#define LWIP_MULTICAST_PING             1

/**
 * TCP_WND: The size of a TCP window.  This must be at least
 * (2 * TCP_MSS) for things to work well.
 * ATTENTION: when using TCP_RCV_SCALE, TCP_WND is the total size
 * with scaling applied. Maximum window value in the TCP header
 * will be TCP_WND >> TCP_RCV_SCALE
 */
#define TCP_WND                         65535

/**
 * TCP_SND_BUF: TCP sender buffer space (bytes).
 * To achieve good performance, this should be at least 2 * TCP_MSS.
 */
#if LWIP_WND_SCALE
#define TCP_SND_BUF                     (64 * TCP_MSS)
#else
#define TCP_SND_BUF                     (32 * TCP_MSS)
#endif

/**
 * TCP_SND_QUEUELEN: TCP sender buffer space (pbufs). This must be at least
 * as much as (2 * TCP_SND_BUF/TCP_MSS) for things to work.
 * 在单个pcb中排队的待发pbuf数量上限
 */
#define TCP_SND_QUEUELEN                ((64 * (TCP_SND_BUF) + (TCP_MSS - 1))/(TCP_MSS))

/**
 * TCP_LISTEN_BACKLOG: Enable the backlog option for tcp listen pcb.
 */
#define TCP_LISTEN_BACKLOG              1

/**
 * LWIP_NETIF_STATUS_CALLBACK==1: Support a callback function whenever an interface
 * changes its up/down status (i.e., due to DHCP IP acquisition)
 */
#define LWIP_NETIF_STATUS_CALLBACK      1

/**
 * LWIP_NETIF_LINK_CALLBACK==1: Support a callback function from an interface
 * whenever the link changes (i.e., link down)
 */
#define LWIP_NETIF_LINK_CALLBACK        1

/*
 * LWIP_NETIF_USE_HINTS==1
 */
#define LWIP_NETIF_USE_HINTS            1

/**
 * LWIP_NETIF_HWADDRHINT==1: Cache link-layer-address hints (e.g. table
 * indices) in struct netif. TCP and UDP can make use of this to prevent
 * scanning the ARP table for every sent packet. While this is faster for big
 * ARP tables or many concurrent connections, it might be counterproductive
 * if you have a tiny ARP table or if there never are concurrent connections.
 */
#define LWIP_NETIF_HWADDRHINT           1

/**
 * SO_REUSE==1: Enable SO_REUSEADDR option.
 */
#define SO_REUSE                        1

/**
 * SO_REUSE_RXTOALL==1: Pass a copy of incoming broadcast/multicast packets
 * to all local matches if SO_REUSEADDR is turned on.
 * WARNING: Adds a memcpy for every packet if passing to more than one pcb!
 */
#define SO_REUSE_RXTOALL                1

/**
 * LWIP_TCP_KEEPALIVE==1: Enable TCP_KEEPIDLE, TCP_KEEPINTVL and TCP_KEEPCNT
 * options processing. Note that TCP_KEEPIDLE and TCP_KEEPINTVL have to be set
 * in seconds. (does not require sockets.c, and will affect tcp.c)
 */
#define LWIP_TCP_KEEPALIVE              0

/*
 * Default KEEPALIVE timer in milliseconds
 */
/* 10秒内连接双方都无数据，则发起保活探测（该值默认为2小时） */
#define TCP_KEEPIDLE_DEFAULT            10000UL

/*
 * Default Time between KEEPALIVE probes in milliseconds
 * 每2秒发送一次保活探测
 */
#define TCP_KEEPINTVL_DEFAULT           2000UL

/*
 * Default Counter for KEEPALIVE probes
 * 保活机制启动后，一共发送9次保活探测包，
 * 如果这9个包对方均无回应，则表示连接异常，
 * 内核关闭连接，并发送err回调到用户程序
 */
#define TCP_KEEPCNT_DEFAULT             9U

/**
 * ARP_TABLE_SIZE: Number of active MAC-IP address pairs cached.
 */
#define ARP_TABLE_SIZE                  128

/**
 * IP_REASS_MAXAGE: Maximum time (in multiples of IP_TMR_INTERVAL - so seconds, normally)
 * a fragmented IP packet waits for all fragments to arrive. If not all fragments arrived
 * in this time, the whole packet is discarded.
 */
#define IP_REASS_MAXAGE                 3

/**
 * LWIP_NUM_NETIF_CLIENT_DATA: Number of clients that may store
 * data in client_data member array of struct netif (max. 256).
 */
#define LWIP_NUM_NETIF_CLIENT_DATA      8

/**
 * LWIP_IPV6_FRAG==1: Fragment outgoing IPv6 packets that are too big.
 */
#define LWIP_IPV6_FRAG                  0

/**
 * LWIP_ICMP6_DATASIZE: bytes from original packet to send back in
 * ICMPv6 error messages (0 = default of IP6_MIN_MTU_LENGTH)
 * ATTENTION: RFC4443 section 2.4 says IP6_MIN_MTU_LENGTH is a MUST,
 * so override this only if you absolutely have to!
 */
#define LWIP_ICMP6_DATASIZE             8

#define LWIP_HOOK_FILENAME              "lwip_hooks.h"

#define LWIP_HOOK_IP4_INPUT             eth_lwip_ip4_input_hook

#define LWIP_HOOK_MEMP_ERROR            eth_lwip_memp_error_hook

#if LWIP_IGMP
#include <stdlib.h>
#endif

#endif
