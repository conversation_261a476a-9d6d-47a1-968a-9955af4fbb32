#include <shell.h>
#include <stdbool.h>
#include <ttos.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <net/netdev.h>
#include <net/ethernet_dev.h>
#include <net/phy_dev.h>

#ifdef CONFIG_SHELL
extern unsigned int nebuf_alloc_err;
extern unsigned int pbuf_alloc_err;
extern unsigned int netconn_alloc_err;
extern unsigned int other_pool_err;

static void af_packet_info_output(ETH_DEV *ethdev)
{
    AFPACKET_STATS *pkt_stats = NULL;
    pkt_stats = &(ethdev->netdev->afpkt_stats);

    printf("%s\n", ethdev->netdev->name);

    if (netdev_is_up(ethdev->netdev))
    {
        printf("        UP ");

        if (ethdev->phydev != NULL)
        {
            if (ethdev->phy_status & IFM_ACTIVE)
            {
                printf("RUNNING ");

                switch (IFM_SUBTYPE (ethdev->phy_media))
                {
                    case IFM_1000_T:
                        printf("1000Mbit ");
                        break;

                    case IFM_100_TX:
                        printf("100Mbit ");
                        break;

                    case IFM_10_T:
                        printf("10Mbit ");
                        break;

                    default:
                        break;
                }

                if (ethdev->phy_media & IFM_FDX)
                {
                    printf("Full Duplex\n");
                }
                else
                {
                    printf("Half Duplex\n");
                }
            }
            else
            {
                printf("\n");
            }
        }
        else
        {
            printf("\n");
        }
    }

    printf("        TX packets:%lld bytes:%lld error packets:%lld error bytes:%lld\n", pkt_stats->out_pacs, pkt_stats->out_bytes, pkt_stats->eout_pacs, pkt_stats->eout_bytes);
    printf("        RX packets:%lld bytes:%lld drop packets:%lld drop bytes:%lld\n", pkt_stats->in_pacs, pkt_stats->in_bytes, pkt_stats->din_pacs, pkt_stats->din_bytes);
}

static int eth_info_output(ETH_DEV *ethdev, void *arg)
{
    (void)arg;

    if (ethdev == NULL)
    {
        return -1;
    }

    if (ethdev->eth_unit == -1)
    {
        return 0;
    }

    af_packet_info_output(ethdev);
}

#ifdef CONFIG_LWIP_2_2_0
static void eth_lwip_err_info()
{
    printf("\nLwIP MemPool ERROR:\n");
    printf("        NETBUF:%d PBUF:%d NETCONN:%d Other:%d\n", nebuf_alloc_err, pbuf_alloc_err, netconn_alloc_err, other_pool_err);
}
#endif

static int eth_info(int argc, char *argv[])
{
    char *eth_name = argv[1];
    ETH_DEV *ethdev = NULL;
    NET_DEV *netdev = NULL;
    AFPACKET_STATS *pkt_stats = NULL;

    if (argc < 2)
    {
        eth_device_iterate(eth_info_output, NULL);
    }
    else
    {
        ethdev = eth_find_by_name(eth_name);
        if (ethdev == NULL || IS_LOOPBACK_DEV(ethdev))
        {
            printk("The specified ethernet device was not found!\n");
            return -1;
        }

        af_packet_info_output(ethdev);
    }

#ifdef CONFIG_LWIP_2_2_0
    eth_lwip_err_info();
#endif

    return 0;
}

SHELL_EXPORT_CMD (SHELL_CMD_PERMISSION (0)
                      | SHELL_CMD_TYPE (SHELL_TYPE_CMD_MAIN)
                      | SHELL_CMD_DISABLE_RETURN,
                  ethinfo, eth_info, Ethernet Infomation);
#endif